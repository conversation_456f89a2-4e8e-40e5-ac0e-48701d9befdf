import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const CreateBattleFAB = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [battleData, setBattleData] = useState({
    title: '',
    category: '',
    difficulty: '',
    duration: '',
    maxParticipants: '',
    language: ''
  });

  const categoryOptions = [
    { value: 'algorithms', label: 'Algorithms' },
    { value: 'frontend', label: 'Frontend' },
    { value: 'fullstack', label: 'Full Stack' },
    { value: 'database', label: 'Database' }
  ];

  const difficultyOptions = [
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' }
  ];

  const languageOptions = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'cpp', label: 'C++' }
  ];

  const durationOptions = [
    { value: '15', label: '15 minutes' },
    { value: '30', label: '30 minutes' },
    { value: '45', label: '45 minutes' },
    { value: '60', label: '60 minutes' }
  ];

  const participantOptions = [
    { value: '2', label: '2 players' },
    { value: '4', label: '4 players' },
    { value: '6', label: '6 players' },
    { value: '8', label: '8 players' },
    { value: '10', label: '10 players' }
  ];

  const handleInputChange = (field, value) => {
    setBattleData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateBattle = () => {
    console.log('Creating battle:', battleData);
    // Reset form and close
    setBattleData({
      title: '',
      category: '',
      difficulty: '',
      duration: '',
      maxParticipants: '',
      language: ''
    });
    setIsExpanded(false);
  };

  const isFormValid = battleData.title && battleData.category && battleData.difficulty && 
                     battleData.duration && battleData.maxParticipants && battleData.language;

  if (isExpanded) {
    return (
      <div className="fixed inset-0 z-[1020] flex items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="absolute inset-0 bg-background/80 backdrop-blur-sm"
          onClick={() => setIsExpanded(false)}
        />
        
        {/* Modal */}
        <div className="relative bg-card border border-border rounded-xl shadow-elevation max-w-md w-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border">
            <h2 className="text-xl font-bold text-foreground">Create Battle</h2>
            <Button variant="ghost" size="icon" onClick={() => setIsExpanded(false)}>
              <Icon name="X" size={20} />
            </Button>
          </div>

          {/* Form */}
          <div className="p-6 space-y-4">
            <Input
              label="Battle Title"
              type="text"
              placeholder="Enter battle title"
              value={battleData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              required
            />

            <Select
              label="Category"
              placeholder="Select category"
              options={categoryOptions}
              value={battleData.category}
              onChange={(value) => handleInputChange('category', value)}
              required
            />

            <Select
              label="Difficulty"
              placeholder="Select difficulty"
              options={difficultyOptions}
              value={battleData.difficulty}
              onChange={(value) => handleInputChange('difficulty', value)}
              required
            />

            <Select
              label="Programming Language"
              placeholder="Select language"
              options={languageOptions}
              value={battleData.language}
              onChange={(value) => handleInputChange('language', value)}
              required
            />

            <div className="grid grid-cols-2 gap-4">
              <Select
                label="Duration"
                placeholder="Duration"
                options={durationOptions}
                value={battleData.duration}
                onChange={(value) => handleInputChange('duration', value)}
                required
              />

              <Select
                label="Max Players"
                placeholder="Players"
                options={participantOptions}
                value={battleData.maxParticipants}
                onChange={(value) => handleInputChange('maxParticipants', value)}
                required
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3 p-6 border-t border-border">
            <Button
              variant="outline"
              onClick={() => setIsExpanded(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="default"
              onClick={handleCreateBattle}
              disabled={!isFormValid}
              className="flex-1"
              iconName="Plus"
              iconPosition="left"
            >
              Create Battle
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Button
      variant="default"
      size="lg"
      onClick={() => setIsExpanded(true)}
      className="fixed bottom-6 right-6 z-[1000] rounded-full w-14 h-14 shadow-lg battle-glow"
    >
      <Icon name="Plus" size={24} />
    </Button>
  );
};

export default CreateBattleFAB;