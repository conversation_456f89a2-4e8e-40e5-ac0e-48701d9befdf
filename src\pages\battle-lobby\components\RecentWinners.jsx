import React from 'react';
import Icon from '../../../components/AppIcon';

const RecentWinners = () => {
  const recentWinners = [
    {
      id: 1,
      name: "<PERSON>",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      battleTitle: "React Hooks Challenge",
      timeAgo: "2 min ago",
      xpEarned: 250
    },
    {
      id: 2,
      name: "<PERSON>",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      battleTitle: "Algorithm Sprint",
      timeAgo: "8 min ago",
      xpEarned: 300
    },
    {
      id: 3,
      name: "<PERSON>",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      battleTitle: "CSS Grid Master",
      timeAgo: "15 min ago",
      xpEarned: 200
    },
    {
      id: 4,
      name: "<PERSON>",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      battleTitle: "Database Query Pro",
      timeAgo: "23 min ago",
      xpEarned: 350
    }
  ];

  return (
    <div className="bg-card border border-border rounded-xl p-6">
      {/* Header */}
      <div className="flex items-center space-x-2 mb-4">
        <Icon name="Trophy" size={20} className="text-warning" />
        <h3 className="font-semibold text-foreground">Recent Winners</h3>
      </div>

      {/* Winners List */}
      <div className="space-y-4">
        {recentWinners.map((winner) => (
          <div key={winner.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors duration-150">
            {/* Avatar */}
            <div className="w-10 h-10 rounded-full overflow-hidden bg-muted flex-shrink-0 relative">
              <img 
                src={winner.avatar} 
                alt={winner.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = '/assets/images/no_image.png';
                }}
              />
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-warning rounded-full flex items-center justify-center">
                <Icon name="Crown" size={12} className="text-background" />
              </div>
            </div>

            {/* Info */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-foreground truncate">
                {winner.name}
              </div>
              <div className="text-sm text-text-secondary truncate">
                {winner.battleTitle}
              </div>
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-xs text-text-secondary">
                  {winner.timeAgo}
                </span>
                <span className="text-xs text-success font-medium">
                  +{winner.xpEarned} XP
                </span>
              </div>
            </div>

            {/* Trophy Icon */}
            <div className="flex-shrink-0">
              <Icon name="Trophy" size={16} className="text-warning" />
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="text-xs text-text-secondary text-center">
          Live updates from completed battles
        </div>
      </div>
    </div>
  );
};

export default RecentWinners;