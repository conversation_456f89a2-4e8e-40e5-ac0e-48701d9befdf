import React, { useState } from 'react';
import Image from '../../../components/AppImage';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const UserRankCard = ({ user, rank, isCurrentUser, showExtendedStats = false }) => {
  const [isFollowing, setIsFollowing] = useState(user.isFollowing || false);
  const [showQuickView, setShowQuickView] = useState(false);

  const getRankIcon = (position) => {
    if (position === 1) return { icon: 'Trophy', color: 'text-yellow-500' };
    if (position === 2) return { icon: 'Medal', color: 'text-gray-400' };
    if (position === 3) return { icon: 'Award', color: 'text-amber-600' };
    return null;
  };

  const handleFollow = (e) => {
    e.stopPropagation();
    setIsFollowing(!isFollowing);
  };

  const handleChallenge = (e) => {
    e.stopPropagation();
    console.log('Challenge user:', user.username);
  };

  const rankIcon = getRankIcon(rank);

  return (
    <div 
      className={`relative p-4 rounded-lg border transition-all duration-200 hover:shadow-md cursor-pointer ${
        isCurrentUser 
          ? 'bg-primary/10 border-primary/30 ring-1 ring-primary/20' :'bg-card border-border hover:border-primary/30'
      }`}
      onMouseEnter={() => setShowQuickView(true)}
      onMouseLeave={() => setShowQuickView(false)}
    >
      <div className="flex items-center space-x-4">
        {/* Rank */}
        <div className="flex items-center justify-center w-12 h-12 rounded-lg bg-muted">
          {rankIcon ? (
            <Icon name={rankIcon.icon} size={20} className={rankIcon.color} />
          ) : (
            <span className="font-bold text-lg">{rank}</span>
          )}
        </div>

        {/* Avatar */}
        <div className="relative">
          <Image
            src={user.avatar}
            alt={user.username}
            className="w-12 h-12 rounded-full object-cover"
          />
          {user.isOnline && (
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-success rounded-full border-2 border-background"></div>
          )}
        </div>

        {/* User Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <h3 className="font-semibold text-foreground truncate">{user.username}</h3>
            {user.badges?.map((badge, index) => (
              <Icon key={index} name={badge.icon} size={16} className={badge.color} />
            ))}
          </div>
          <div className="flex items-center space-x-4 text-sm text-text-secondary">
            <span className="flex items-center space-x-1">
              <Icon name="Zap" size={14} />
              <span>{user.xp.toLocaleString()} XP</span>
            </span>
            <span className="flex items-center space-x-1">
              <Icon name="Target" size={14} />
              <span>{user.winRate}% win rate</span>
            </span>
            {user.streak > 0 && (
              <span className="flex items-center space-x-1 text-warning">
                <Icon name="Flame" size={14} />
                <span>{user.streak} streak</span>
              </span>
            )}
          </div>
        </div>

        {/* Extended Stats (Desktop) */}
        {showExtendedStats && (
          <div className="hidden lg:flex items-center space-x-6 text-sm">
            <div className="text-center">
              <div className="font-semibold text-foreground">{user.battlesWon}</div>
              <div className="text-text-secondary">Wins</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-foreground">{user.avgSolveTime}s</div>
              <div className="text-text-secondary">Avg Time</div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-foreground">{user.favoriteLanguage}</div>
              <div className="text-text-secondary">Language</div>
            </div>
          </div>
        )}

        {/* Actions */}
        {!isCurrentUser && (
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleFollow}
              iconName={isFollowing ? 'UserMinus' : 'UserPlus'}
              iconSize={16}
            >
              {isFollowing ? 'Unfollow' : 'Follow'}
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleChallenge}
              iconName="Swords"
              iconSize={16}
            >
              Challenge
            </Button>
          </div>
        )}
      </div>

      {/* Position Change Indicator */}
      {user.positionChange && (
        <div className={`absolute top-2 right-2 flex items-center space-x-1 text-xs px-2 py-1 rounded-full ${
          user.positionChange > 0 
            ? 'bg-success/20 text-success' :'bg-error/20 text-error'
        }`}>
          <Icon 
            name={user.positionChange > 0 ? 'TrendingUp' : 'TrendingDown'} 
            size={12} 
          />
          <span>{Math.abs(user.positionChange)}</span>
        </div>
      )}

      {/* Quick View Tooltip (Desktop) */}
      {showQuickView && !isCurrentUser && (
        <div className="hidden md:block absolute top-full left-0 mt-2 w-80 bg-popover border border-border rounded-lg shadow-elevation z-10 p-4">
          <div className="flex items-center space-x-3 mb-3">
            <Image
              src={user.avatar}
              alt={user.username}
              className="w-16 h-16 rounded-full object-cover"
            />
            <div>
              <h4 className="font-semibold text-foreground">{user.username}</h4>
              <p className="text-sm text-text-secondary">Level {user.level} • {user.xp.toLocaleString()} XP</p>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-text-secondary">Recent Activity:</span>
              <p className="text-foreground">{user.recentActivity}</p>
            </div>
            <div>
              <span className="text-text-secondary">Specialization:</span>
              <p className="text-foreground">{user.specialization}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserRankCard;