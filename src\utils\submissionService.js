import { supabase } from './supabaseClient';

const submissionService = {
  // Create or update submission
  async saveSubmission(battleId, submissionData) {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('submissions')
        .upsert({
          battle_id: battleId,
          user_id: user.user.id,
          ...submissionData,
          updated_at: new Date().toISOString()
        })
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url)
        `)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to save submission' }
    }
  },

  // Submit final submission
  async submitSubmission(battleId, submissionData) {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('submissions')
        .upsert({
          battle_id: battleId,
          user_id: user.user.id,
          ...submissionData,
          status: 'submitted',
          submitted_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url)
        `)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to submit code' }
    }
  },

  // Get user's submission for a battle
  async getUserSubmission(battleId) {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('submissions')
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url)
        `)
        .eq('battle_id', battleId)
        .eq('user_id', user.user.id)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load submission' }
    }
  },

  // Get all submissions for a battle
  async getBattleSubmissions(battleId) {
    try {
      const { data, error } = await supabase
        .from('submissions')
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url, xp)
        `)
        .eq('battle_id', battleId)
        .eq('status', 'submitted')
        .order('submitted_at', { ascending: true })

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load submissions' }
    }
  },

  // Get submission by ID (for viewing specific submission)
  async getSubmissionById(submissionId) {
    try {
      const { data, error } = await supabase
        .from('submissions')
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url, xp),
          battle:battles(
            id,
            title,
            status,
            prompt:prompts(*)
          )
        `)
        .eq('id', submissionId)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load submission' }
    }
  },

  // Test/validate code (mock implementation for now)
  async testSubmission(battleId, code, language) {
    try {
      // This would normally send code to a code execution service
      // For now, we'll simulate basic validation
      const hasFunction = code.includes('function') || code.includes('def') || code.includes('class')
      const hasReturn = code.includes('return')
      
      const testResults = {
        passed: hasFunction && hasReturn,
        executionTime: Math.floor(Math.random() * 1000) + 100, // Random 100-1100ms
        memoryUsed: Math.floor(Math.random() * 50) + 10, // Random 10-60MB
        testCases: [
          { input: 'Test case 1', expected: 'Expected output 1', actual: 'Actual output 1', passed: true },
          { input: 'Test case 2', expected: 'Expected output 2', actual: 'Expected output 2', passed: true },
          { input: 'Test case 3', expected: 'Expected output 3', actual: 'Different output', passed: false }
        ]
      }

      // Save test results
      await this.saveSubmission(battleId, {
        code,
        language,
        status: testResults.passed ? 'passed' : 'failed',
        execution_time_ms: testResults.executionTime,
        memory_used_mb: testResults.memoryUsed,
        test_results: testResults
      })

      return { success: true, data: testResults }
    } catch (error) {
      return { success: false, error: 'Failed to test submission' }
    }
  },

  // Subscribe to submission changes for a battle
  subscribeToSubmissions(battleId, onUpdate) {
    const channel = supabase
      .channel(`submissions-${battleId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'submissions',
          filter: `battle_id=eq.${battleId}`
        },
        onUpdate
      )
      .subscribe()

    return channel
  }
}

export default submissionService