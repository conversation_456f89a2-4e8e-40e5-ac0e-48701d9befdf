import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const BattleTimer = ({ 
  timeRemaining, 
  totalTime, 
  battleStatus, 
  participantCount, 
  onExitBattle,
  isSpectator 
}) => {
  const [showExitConfirm, setShowExitConfirm] = useState(false);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimeColor = () => {
    const percentage = (timeRemaining / totalTime) * 100;
    if (percentage > 50) return 'text-success';
    if (percentage > 20) return 'text-warning';
    return 'text-error';
  };

  const getProgressColor = () => {
    const percentage = (timeRemaining / totalTime) * 100;
    if (percentage > 50) return 'bg-success';
    if (percentage > 20) return 'bg-warning';
    return 'bg-error';
  };

  const handleExitClick = () => {
    setShowExitConfirm(true);
  };

  const handleConfirmExit = () => {
    onExitBattle();
    setShowExitConfirm(false);
  };

  const handleCancelExit = () => {
    setShowExitConfirm(false);
  };

  return (
    <>
      <div className="bg-card border-b border-border px-6 py-3">
        <div className="flex items-center justify-between">
          {/* Left Section - Battle Info */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full animate-pulse ${
                battleStatus === 'active' ? 'bg-success' :
                battleStatus === 'starting' ? 'bg-warning' :
                battleStatus === 'ended' ? 'bg-error' : 'bg-muted'
              }`}></div>
              <span className="text-sm font-medium text-foreground capitalize">
                {battleStatus === 'active' ? 'Battle Active' :
                 battleStatus === 'starting' ? 'Starting Soon' :
                 battleStatus === 'ended' ? 'Battle Ended' : 'Waiting'}
              </span>
            </div>

            <div className="flex items-center space-x-1 text-sm text-text-secondary">
              <Icon name="Users" size={16} />
              <span>{participantCount} players</span>
            </div>

            {isSpectator && (
              <div className="flex items-center space-x-1 text-sm text-warning">
                <Icon name="Eye" size={16} />
                <span>Spectating</span>
              </div>
            )}
          </div>

          {/* Center Section - Timer */}
          <div className="flex items-center space-x-4">
            <div className="text-center">
              <div className={`text-2xl font-mono font-bold ${getTimeColor()}`}>
                {formatTime(timeRemaining)}
              </div>
              <div className="text-xs text-text-secondary">Time Remaining</div>
            </div>

            {/* Progress Ring */}
            <div className="relative w-12 h-12">
              <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                <path
                  className="text-muted"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="transparent"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className={getTimeColor()}
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="transparent"
                  strokeDasharray={`${(timeRemaining / totalTime) * 100}, 100`}
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <Icon name="Clock" size={16} className={getTimeColor()} />
              </div>
            </div>
          </div>

          {/* Right Section - Actions */}
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleExitClick}
              className="text-error border-error hover:bg-error hover:text-white"
            >
              <Icon name="LogOut" size={16} />
              {isSpectator ? 'Leave' : 'Exit Battle'}
            </Button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-3">
          <div className="w-full bg-muted rounded-full h-1">
            <div 
              className={`h-1 rounded-full transition-all duration-1000 ${getProgressColor()}`}
              style={{ width: `${(timeRemaining / totalTime) * 100}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Exit Confirmation Modal */}
      {showExitConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[1000]">
          <div className="bg-card border border-border rounded-lg p-6 w-96 max-w-[90vw]">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-error/20 rounded-full flex items-center justify-center">
                <Icon name="AlertTriangle" size={20} className="text-error" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground">
                  {isSpectator ? 'Leave Battle?' : 'Exit Battle?'}
                </h3>
                <p className="text-sm text-text-secondary">
                  {isSpectator 
                    ? 'You will stop spectating this battle.' :'You will forfeit this battle and lose any progress.'
                  }
                </p>
              </div>
            </div>

            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleCancelExit}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleConfirmExit}
                className="flex-1"
              >
                <Icon name="LogOut" size={16} />
                {isSpectator ? 'Leave' : 'Exit'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BattleTimer;