import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const CreatePrompt: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    text: '',
    category: 'frontend',
    difficulty: 'medium',
    maxChars: '',
    language: '',
    bannedKeywords: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement prompt creation
    console.log('Creating prompt:', formData);
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <div className="card">
          <h1 className="text-3xl font-bold text-white mb-8">Create New Prompt</h1>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Prompt Text */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Prompt Text *
              </label>
              <textarea
                value={formData.text}
                onChange={(e) => setFormData({ ...formData, text: e.target.value })}
                className="input-field w-full h-32 resize-none"
                placeholder="Enter the coding challenge prompt..."
                required
              />
            </div>

            {/* Category and Difficulty */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Category *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  className="input-field w-full"
                  required
                >
                  <option value="frontend">Frontend</option>
                  <option value="backend">Backend</option>
                  <option value="algorithm">Algorithm</option>
                  <option value="ui/ux">UI/UX</option>
                  <option value="game">Game</option>
                  <option value="api">API</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Difficulty *
                </label>
                <select
                  value={formData.difficulty}
                  onChange={(e) => setFormData({ ...formData, difficulty: e.target.value })}
                  className="input-field w-full"
                  required
                >
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                  <option value="expert">Expert</option>
                </select>
              </div>
            </div>

            {/* Constraints */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Max Characters (optional)
                </label>
                <input
                  type="number"
                  value={formData.maxChars}
                  onChange={(e) => setFormData({ ...formData, maxChars: e.target.value })}
                  className="input-field w-full"
                  placeholder="e.g., 1000"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Language (optional)
                </label>
                <input
                  type="text"
                  value={formData.language}
                  onChange={(e) => setFormData({ ...formData, language: e.target.value })}
                  className="input-field w-full"
                  placeholder="e.g., JavaScript, Python"
                />
              </div>
            </div>

            {/* Banned Keywords */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Banned Keywords (optional)
              </label>
              <input
                type="text"
                value={formData.bannedKeywords}
                onChange={(e) => setFormData({ ...formData, bannedKeywords: e.target.value })}
                className="input-field w-full"
                placeholder="e.g., eval, innerHTML, document.write (comma-separated)"
              />
              <p className="text-xs text-gray-500 mt-1">
                Keywords that players cannot use in their solutions
              </p>
            </div>

            {/* Submit Buttons */}
            <div className="flex space-x-4 pt-6">
              <button
                type="submit"
                className="btn-primary flex-1"
              >
                Create Prompt
              </button>
              <button
                type="button"
                onClick={() => navigate('/')}
                className="btn-secondary flex-1"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreatePrompt; 