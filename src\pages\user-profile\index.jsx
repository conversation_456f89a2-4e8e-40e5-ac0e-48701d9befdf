import React, { useState, useEffect } from 'react';
import Header from '../../components/ui/Header';
import BattleQuickJoin from '../../components/ui/BattleQuickJoin';
import ProfileHeader from './components/ProfileHeader';
import ProfileTabs from './components/ProfileTabs';
import OverviewTab from './components/OverviewTab';
import BattleHistoryTab from './components/BattleHistoryTab';
import SettingsTab from './components/SettingsTab';

const UserProfile = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);

  // Mock user data
  const [user] = useState({
    id: 'user_001',
    name: '<PERSON>',
    username: 'alexcodes',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    bio: 'Full-stack developer passionate about algorithms and competitive programming. Love solving complex problems and learning new technologies.',
    location: 'San Francisco, CA',
    website: 'https://alexchen.dev',
    joinDate: '2023-03-15',
    isVerified: true,
    level: 12,
    xp: 2450,
    totalBattles: 87,
    winRate: 73,
    currentStreak: 5,
    rank: 42,
    // Settings
    profileVisibility: 'public',
    showEmail: false,
    showLocation: true,
    allowFriendRequests: true,
    showOnlineStatus: true,
    emailNotifications: true,
    battleInvites: true,
    friendRequests: true,
    achievementUnlocks: true,
    weeklyDigest: true,
    marketingEmails: false,
    preferredDifficulty: 'medium',
    autoJoinBattles: false,
    showCodeDuringBattle: true,
    allowSpectators: true,
    theme: 'dark',
    language: 'en',
    timezone: 'America/Los_Angeles'
  });

  // Mock achievements data
  const achievements = [
    {
      id: 1,
      name: 'First Victory',
      description: 'Win your first battle',
      icon: '🏆',
      unlocked: true
    },
    {
      id: 2,
      name: 'Speed Demon',
      description: 'Complete a battle in under 5 minutes',
      icon: '⚡',
      unlocked: true
    },
    {
      id: 3,
      name: 'Algorithm Master',
      description: 'Win 10 algorithm battles',
      icon: '🧠',
      unlocked: true
    },
    {
      id: 4,
      name: 'Streak Master',
      description: 'Win 5 battles in a row',
      icon: '🔥',
      unlocked: true
    },
    {
      id: 5,
      name: 'Community Favorite',
      description: 'Receive 100 votes',
      icon: '❤️',
      unlocked: false
    },
    {
      id: 6,
      name: 'Code Warrior',
      description: 'Participate in 50 battles',
      icon: '⚔️',
      unlocked: true
    },
    {
      id: 7,
      name: 'Perfect Score',
      description: 'Get maximum votes in a battle',
      icon: '💯',
      unlocked: false
    },
    {
      id: 8,
      name: 'Night Owl',
      description: 'Win a battle after midnight',
      icon: '🦉',
      unlocked: true
    }
  ];

  // Mock skill ratings
  const skillRatings = [
    { category: 'Algorithms', rating: 1850 },
    { category: 'Data Structures', rating: 1720 },
    { category: 'Web Development', rating: 1650 },
    { category: 'System Design', rating: 1420 },
    { category: 'Database Design', rating: 1380 }
  ];

  // Mock recent activity
  const recentActivity = [
    {
      id: 1,
      type: 'battle_won',
      description: 'Won "Binary Tree Traversal" battle',
      timestamp: '2 hours ago'
    },
    {
      id: 2,
      type: 'achievement',
      description: 'Unlocked "Streak Master" achievement',
      timestamp: '4 hours ago'
    },
    {
      id: 3,
      type: 'battle_lost',
      description: 'Lost "Dynamic Programming Challenge"',
      timestamp: '1 day ago'
    },
    {
      id: 4,
      type: 'level_up',
      description: 'Reached Level 12',
      timestamp: '2 days ago'
    },
    {
      id: 5,
      type: 'battle_won',
      description: 'Won "React Component Design" battle',
      timestamp: '3 days ago'
    },
    {
      id: 6,
      type: 'battle_won',
      description: 'Won "SQL Query Optimization" battle',
      timestamp: '4 days ago'
    }
  ];

  // Mock performance data
  const performanceData = {
    xpProgress: [
      { date: 'Jan', xp: 1200 },
      { date: 'Feb', xp: 1450 },
      { date: 'Mar', xp: 1680 },
      { date: 'Apr', xp: 1920 },
      { date: 'May', xp: 2150 },
      { date: 'Jun', xp: 2380 },
      { date: 'Jul', xp: 2450 }
    ],
    battleResults: [
      { name: 'Won', value: 63 },
      { name: 'Lost', value: 21 },
      { name: 'Draw', value: 3 }
    ]
  };

  // Mock battle history
  const battleHistory = [
    {
      id: 'battle_001',
      title: 'Binary Tree Traversal',
      category: 'algorithms',
      difficulty: 'medium',
      result: 'won',
      participants: 8,
      duration: 420,
      xpChange: 85,
      date: '2025-01-27',
      placement: 1,
      votes: 12
    },
    {
      id: 'battle_002',
      title: 'Dynamic Programming Challenge',
      category: 'algorithms',
      difficulty: 'hard',
      result: 'lost',
      participants: 12,
      duration: 600,
      xpChange: -25,
      date: '2025-01-26',
      placement: 8,
      votes: 3
    },
    {
      id: 'battle_003',
      title: 'React Component Design',
      category: 'web-dev',
      difficulty: 'medium',
      result: 'won',
      participants: 6,
      duration: 480,
      xpChange: 75,
      date: '2025-01-24',
      placement: 2,
      votes: 8
    },
    {
      id: 'battle_004',
      title: 'SQL Query Optimization',
      category: 'data-structures',
      difficulty: 'easy',
      result: 'won',
      participants: 10,
      duration: 300,
      xpChange: 45,
      date: '2025-01-23',
      placement: 1,
      votes: 15
    },
    {
      id: 'battle_005',
      title: 'System Architecture Design',
      category: 'system-design',
      difficulty: 'hard',
      result: 'draw',
      participants: 4,
      duration: 900,
      xpChange: 10,
      date: '2025-01-22',
      placement: 2,
      votes: 6
    },
    {
      id: 'battle_006',
      title: 'Array Manipulation',
      category: 'algorithms',
      difficulty: 'easy',
      result: 'won',
      participants: 15,
      duration: 240,
      xpChange: 35,
      date: '2025-01-21',
      placement: 3,
      votes: 9
    },
    {
      id: 'battle_007',
      title: 'Graph Algorithms',
      category: 'algorithms',
      difficulty: 'hard',
      result: 'lost',
      participants: 8,
      duration: 720,
      xpChange: -30,
      date: '2025-01-20',
      placement: 6,
      votes: 2
    },
    {
      id: 'battle_008',
      title: 'CSS Grid Layout',
      category: 'web-dev',
      difficulty: 'medium',
      result: 'won',
      participants: 7,
      duration: 360,
      xpChange: 65,
      date: '2025-01-19',
      placement: 1,
      votes: 11
    }
  ];

  const handleEditProfile = () => {
    setIsEditing(!isEditing);
    if (!isEditing) {
      setActiveTab('settings');
    }
  };

  const handleSaveSettings = async (newSettings) => {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Settings saved:', newSettings);
        setIsEditing(false);
        resolve();
      }, 1000);
    });
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <OverviewTab
            user={user}
            achievements={achievements}
            skillRatings={skillRatings}
            recentActivity={recentActivity}
            performanceData={performanceData}
          />
        );
      case 'history':
        return <BattleHistoryTab battleHistory={battleHistory} />;
      case 'settings':
        return <SettingsTab user={user} onSaveSettings={handleSaveSettings} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    document.title = `${user.name} - PromptClash Profile`;
  }, [user.name]);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <BattleQuickJoin />
      
      <main className="pt-20 pb-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <ProfileHeader
            user={user}
            onEditProfile={handleEditProfile}
            isEditing={isEditing}
          />
          
          <ProfileTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
          
          {renderTabContent()}
        </div>
      </main>
    </div>
  );
};

export default UserProfile;