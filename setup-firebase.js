// Simple Firebase Setup Helper
// This script will help you set up Firebase manually

console.log('🚀 PromptClash Firebase Setup Helper');
console.log('===================================\n');

console.log('📋 STEP-BY-STEP SETUP INSTRUCTIONS:\n');

console.log('1️⃣ ENABLE GOOGLE AUTHENTICATION:');
console.log('   • Go to: https://console.firebase.google.com/project/vibecodefeed/authentication/providers');
console.log('   • Click "Sign-in method" tab');
console.log('   • Click "Google" provider');
console.log('   • Click "Enable"');
console.log('   • Add your email as project support email');
console.log('   • Click "Save"\n');

console.log('2️⃣ CREATE FIRESTORE DATABASE:');
console.log('   • Go to: https://console.firebase.google.com/project/vibecodefeed/firestore');
console.log('   • Click "Create database"');
console.log('   • Choose "Start in test mode"');
console.log('   • Pick a location (closest to you)');
console.log('   • Click "Done"\n');

console.log('3️⃣ CREATE REALTIME DATABASE:');
console.log('   • Go to: https://console.firebase.google.com/project/vibecodefeed/realtime-database');
console.log('   • Click "Create database"');
console.log('   • Choose "Start in test mode"');
console.log('   • Pick a location (closest to you)');
console.log('   • Click "Done"\n');

console.log('4️⃣ APPLY FIRESTORE SECURITY RULES:');
console.log('   • Go to: https://console.firebase.google.com/project/vibecodefeed/firestore/rules');
console.log('   • Replace existing rules with the content from firestore.rules file');
console.log('   • Click "Publish"\n');

console.log('5️⃣ APPLY REALTIME DATABASE RULES:');
console.log('   • Go to: https://console.firebase.google.com/project/vibecodefeed/realtime-database/rules');
console.log('   • Replace existing rules with the content from database.rules.json file');
console.log('   • Click "Publish"\n');

console.log('6️⃣ ADD SAMPLE PROMPTS:');
console.log('   • Go to: https://console.firebase.google.com/project/vibecodefeed/firestore');
console.log('   • Click "Start collection"');
console.log('   • Collection ID: prompts');
console.log('   • Add documents with auto-generated IDs');
console.log('   • Use the sample data from setup-sample-data.js\n');

console.log('🎯 QUICK LINKS:');
console.log('   🔐 Auth: https://console.firebase.google.com/project/vibecodefeed/authentication/providers');
console.log('   📊 Firestore: https://console.firebase.google.com/project/vibecodefeed/firestore');
console.log('   ⚡ Realtime DB: https://console.firebase.google.com/project/vibecodefeed/realtime-database');
console.log('   🔒 Firestore Rules: https://console.firebase.google.com/project/vibecodefeed/firestore/rules');
console.log('   🔒 Realtime Rules: https://console.firebase.google.com/project/vibecodefeed/realtime-database/rules\n');

console.log('✅ FILES READY:');
console.log('   • .env - Your Firebase configuration');
console.log('   • firestore.rules - Firestore security rules');
console.log('   • database.rules.json - Realtime Database rules');
console.log('   • setup-sample-data.js - Sample prompts data');
console.log('   • firebase.json - Firebase project configuration');
console.log('   • firestore.indexes.json - Firestore indexes\n');

console.log('🚀 TEST YOUR SETUP:');
console.log('   • Open: http://localhost:3001/');
console.log('   • Click "Sign In to Battle"');
console.log('   • Sign in with Google');
console.log('   • Try creating a prompt or joining a battle\n');

console.log('🎉 Once you complete these steps, your PromptClash will be fully functional!'); 