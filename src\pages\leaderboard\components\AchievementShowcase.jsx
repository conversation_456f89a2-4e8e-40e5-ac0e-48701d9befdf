import React from 'react';
import Icon from '../../../components/AppIcon';

const AchievementShowcase = ({ achievements }) => {
  if (!achievements || achievements.length === 0) return null;

  return (
    <div className="bg-card border border-border rounded-lg p-4 mb-6">
      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
        <Icon name="Award" size={20} className="text-warning" />
        <span>Recent Achievements</span>
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {achievements.map((achievement) => (
          <div
            key={achievement.id}
            className="flex items-center space-x-3 p-3 bg-muted rounded-lg hover:bg-muted/80 transition-colors duration-150"
          >
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${achievement.bgColor}`}>
              <Icon name={achievement.icon} size={20} className={achievement.iconColor} />
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-foreground truncate">{achievement.title}</h4>
              <p className="text-sm text-text-secondary truncate">{achievement.description}</p>
              <p className="text-xs text-text-secondary">{achievement.earnedAt}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AchievementShowcase;