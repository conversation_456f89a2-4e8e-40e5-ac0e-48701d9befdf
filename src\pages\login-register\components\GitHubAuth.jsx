import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const GitHubAuth = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const handleGitHubAuth = async () => {
    setIsLoading(true);
    
    // Simulate GitHub OAuth flow
    setTimeout(() => {
      localStorage.setItem('user', JSON.stringify({
        id: 'user_github_001',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        xp: 1850,
        level: 9,
        provider: 'github',
        githubUsername: 'sarah-codes',
        loginTime: new Date().toISOString()
      }));
      navigate('/battle-lobby');
      setIsLoading(false);
    }, 2000);
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-border"></div>
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-4 bg-background text-text-secondary">Or continue with</span>
        </div>
      </div>
      
      <Button
        variant="outline"
        onClick={handleGitHubAuth}
        loading={isLoading}
        fullWidth
        className="border-border hover:bg-muted"
      >
        <Icon name="Github" size={20} className="mr-2" />
        {isLoading ? 'Connecting to GitHub...' : 'Continue with GitHub'}
      </Button>
      
      <p className="text-xs text-text-secondary text-center">
        Join thousands of developers already competing on PromptClash
      </p>
    </div>
  );
};

export default GitHubAuth;