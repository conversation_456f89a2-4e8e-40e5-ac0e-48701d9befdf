import { initializeApp } from 'firebase/app'
import { 
  getAuth, 
  signInWithPopup, 
  GoogleAuthProvider, 
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth'
import { 
  getFirestore, 
  doc, 
  setDoc, 
  getDoc, 
  collection, 
  addDoc, 
  updateDoc, 
  onSnapshot,
  query,
  orderBy,
  limit,
  getDocs
} from 'firebase/firestore'
import { 
  getDatabase, 
  ref, 
  set, 
  push, 
  onValue, 
  off 
} from 'firebase/database'
import { User, Prompt, Battle, Vote } from '../types'

// Firebase configuration
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY || 'placeholder',
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || 'placeholder.firebaseapp.com',
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || 'placeholder',
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || 'placeholder.appspot.com',
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '123456789',
  appId: import.meta.env.VITE_FIREBASE_APP_ID || '1:123456789:web:abcdef123456',
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL || 'https://placeholder-default-rtdb.firebaseio.com'
}

// Initialize Firebase
let app: any, auth: any, db: any, realtimeDb: any

try {
  app = initializeApp(firebaseConfig)
  auth = getAuth(app)
  db = getFirestore(app)
  realtimeDb = getDatabase(app)
} catch (error) {
  console.warn('Firebase initialization failed:', error)
}

// Auth functions
export const signInWithGoogle = async (): Promise<User> => {
  const provider = new GoogleAuthProvider()
  const result = await signInWithPopup(auth, provider)
  const user = result.user
  
  // Create or update user profile
  const userProfile: User = {
    id: user.uid,
    username: user.displayName || 'Anonymous',
    email: user.email || '',
    avatar_url: user.photoURL || undefined,
    xp: 0,
    total_battles: 0,
    wins: 0,
    losses: 0,
    win_streak: 0,
    created_at: new Date()
  }
  
  await setDoc(doc(db, 'users', user.uid), userProfile)
  return userProfile
}

export const signOut = async (): Promise<void> => {
  await firebaseSignOut(auth)
}

export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
    if (firebaseUser) {
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid))
      if (userDoc.exists()) {
        callback(userDoc.data() as User)
      } else {
        // Create new user profile
        const userProfile: User = {
          id: firebaseUser.uid,
          username: firebaseUser.displayName || 'Anonymous',
          email: firebaseUser.email || '',
          avatar_url: firebaseUser.photoURL || undefined,
          xp: 0,
          total_battles: 0,
          wins: 0,
          losses: 0,
          win_streak: 0,
          created_at: new Date()
        }
        await setDoc(doc(db, 'users', firebaseUser.uid), userProfile)
        callback(userProfile)
      }
    } else {
      callback(null)
    }
  })
}

// User profile functions
export const updateUserProfile = async (userId: string, updates: Partial<User>): Promise<void> => {
  await updateDoc(doc(db, 'users', userId), updates)
}

export const getUserProfile = async (userId: string): Promise<User | null> => {
  const userDoc = await getDoc(doc(db, 'users', userId))
  return userDoc.exists() ? userDoc.data() as User : null
}

// Battle functions
export const createBattle = async (battle: Omit<Battle, 'id' | 'created_at'>): Promise<string> => {
  const battleRef = await addDoc(collection(db, 'battles'), {
    ...battle,
    created_at: new Date()
  })
  return battleRef.id
}

export const getBattle = async (battleId: string): Promise<Battle | null> => {
  const battleDoc = await getDoc(doc(db, 'battles', battleId))
  return battleDoc.exists() ? { id: battleDoc.id, ...battleDoc.data() } as Battle : null
}

export const updateBattle = async (battleId: string, updates: Partial<Battle>): Promise<void> => {
  await updateDoc(doc(db, 'battles', battleId), updates)
}

export const listenToBattle = (battleId: string, callback: (battle: Battle | null) => void) => {
  return onSnapshot(doc(db, 'battles', battleId), (doc) => {
    callback(doc.exists() ? { id: doc.id, ...doc.data() } as Battle : null)
  })
}

// Real-time code sync
export const syncCode = async (battleId: string, userId: string, code: string): Promise<void> => {
  await set(ref(realtimeDb, `battles/${battleId}/code/${userId}`), {
    code,
    timestamp: Date.now()
  })
}

export const listenToCode = (battleId: string, userId: string, callback: (code: string) => void) => {
  const codeRef = ref(realtimeDb, `battles/${battleId}/code/${userId}`)
  onValue(codeRef, (snapshot) => {
    const data = snapshot.val()
    if (data && data.code) {
      callback(data.code)
    }
  })
  
  return () => off(codeRef)
}

// Typing indicators
export const setTyping = async (battleId: string, userId: string, isTyping: boolean): Promise<void> => {
  await set(ref(realtimeDb, `battles/${battleId}/typing/${userId}`), {
    isTyping,
    timestamp: Date.now()
  })
}

export const listenToTyping = (battleId: string, callback: (typing: { [key: string]: boolean }) => void) => {
  const typingRef = ref(realtimeDb, `battles/${battleId}/typing`)
  onValue(typingRef, (snapshot) => {
    const data = snapshot.val()
    if (data) {
      const typing: { [key: string]: boolean } = {}
      Object.keys(data).forEach(userId => {
        typing[userId] = data[userId].isTyping
      })
      callback(typing)
    } else {
      callback({})
    }
  })
  
  return () => off(typingRef)
}

// Voting functions
export const submitVote = async (vote: Omit<Vote, 'id' | 'created_at'>): Promise<void> => {
  await addDoc(collection(db, 'votes'), {
    ...vote,
    created_at: new Date()
  })
}

export const getBattleVotes = async (battleId: string): Promise<Vote[]> => {
  const votesQuery = query(
    collection(db, 'votes'),
    orderBy('created_at', 'desc')
  )
  const votesSnapshot = await getDocs(votesQuery)
  return votesSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Vote)
}

// Prompt functions
export const createPrompt = async (prompt: Omit<Prompt, 'id' | 'created_at'>): Promise<string> => {
  const promptRef = await addDoc(collection(db, 'prompts'), {
    ...prompt,
    created_at: new Date()
  })
  return promptRef.id
}

export const getPrompts = async (): Promise<Prompt[]> => {
  const promptsQuery = query(
    collection(db, 'prompts'),
    orderBy('created_at', 'desc'),
    limit(20)
  )
  const promptsSnapshot = await getDocs(promptsQuery)
  return promptsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Prompt)
}

export const getRandomPrompt = async (): Promise<Prompt | null> => {
  const promptsQuery = query(collection(db, 'prompts'))
  const promptsSnapshot = await getDocs(promptsQuery)
  const prompts = promptsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }) as Prompt)
  
  if (prompts.length === 0) return null
  
  const randomIndex = Math.floor(Math.random() * prompts.length)
  return prompts[randomIndex]
}

// Cleanup functions
export const cleanupListeners = (battleId: string) => {
  off(ref(realtimeDb, `battles/${battleId}`))
} 