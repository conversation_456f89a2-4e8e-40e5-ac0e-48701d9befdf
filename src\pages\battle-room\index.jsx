import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Header from '../../components/ui/Header';
import BattleQuickJoin from '../../components/ui/BattleQuickJoin';
import BattleTimer from './components/BattleTimer';
import PromptPanel from './components/PromptPanel';
import CodeEditor from './components/CodeEditor';
import RightPanel from './components/RightPanel';

const BattleRoom = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Battle state
  const [battleData] = useState({
    id: 'battle_001',
    title: 'Algorithm Sprint Challenge',
    status: 'active',
    timeRemaining: 1200, // 20 minutes
    totalTime: 1800, // 30 minutes
    participantCount: 15
  });

  // User state
  const [currentUser] = useState({
    id: 'user_001',
    name: '<PERSON>',
    level: 12,
    isSpectator: false
  });

  // UI state
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(battleData.timeRemaining);

  // Code editor state
  const [selectedLanguage, setSelectedLanguage] = useState('javascript');
  const [code, setCode] = useState(`// Welcome to PromptClash Battle Room!
// Solve the problem below and submit your solution

function solution() {
    // Your code here
    
}`);
  const [fontSize, setFontSize] = useState(16);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationStatus, setValidationStatus] = useState('ready');

  // Mock prompt data
  const [promptData] = useState({
    title: "Two Sum Problem",
    category: "Arrays & Hashing",
    difficulty: "Easy",
    timeLimit: 30,
    description: `Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\n\nYou may assume that each input would have exactly one solution, and you may not use the same element twice.\n\nYou can return the answer in any order.`,
    inputFormat: "The first line contains the array of integers. The second line contains the target integer.",
    outputFormat: "Return an array of two indices that add up to the target.",
    examples: [
      {
        input: "nums = [2,7,11,15], target = 9",
        output: "[0,1]",
        explanation: "Because nums[0] + nums[1] == 9, we return [0, 1]."
      },
      {
        input: "nums = [3,2,4], target = 6",
        output: "[1,2]",
        explanation: "Because nums[1] + nums[2] == 6, we return [1, 2]."
      }
    ],
    constraints: [
      "2 ≤ nums.length ≤ 10⁴",
      "-10⁹ ≤ nums[i] ≤ 10⁹",
      "-10⁹ ≤ target ≤ 10⁹",
      "Only one valid answer exists"
    ]
  });

  // Mock participants data
  const [participants] = useState([
    {
      id: 'user_001',
      name: 'Alex Chen',
      level: 12,
      status: 'coding',
      progress: 65
    },
    {
      id: 'user_002',
      name: 'Sarah Johnson',
      level: 15,
      status: 'submitted',
      progress: 100
    },
    {
      id: 'user_003',
      name: 'Mike Rodriguez',
      level: 8,
      status: 'coding',
      progress: 45
    },
    {
      id: 'user_004',
      name: 'Emily Davis',
      level: 11,
      status: 'idle',
      progress: 20
    },
    {
      id: 'user_005',
      name: 'David Kim',
      level: 13,
      status: 'coding',
      progress: 80
    }
  ]);

  // Mock chat messages
  const [chatMessages, setChatMessages] = useState([
    {
      id: 1,
      senderId: 'user_002',
      senderName: 'Sarah Johnson',
      content: 'Good luck everyone! 🚀',
      timestamp: new Date(Date.now() - 300000)
    },
    {
      id: 2,
      senderId: 'user_003',
      senderName: 'Mike Rodriguez',
      content: 'This problem looks tricky but doable',
      timestamp: new Date(Date.now() - 240000)
    },
    {
      id: 3,
      senderId: 'user_001',
      senderName: 'Alex Chen',
      content: 'Anyone else thinking hash map approach?',
      timestamp: new Date(Date.now() - 180000)
    },
    {
      id: 4,
      senderId: 'user_005',
      senderName: 'David Kim',
      content: 'Yeah, O(n) time complexity should work',
      timestamp: new Date(Date.now() - 120000)
    }
  ]);

  // Timer countdown effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 0) {
          clearInterval(timer);
          // Navigate to results when time is up
          navigate('/battle-results');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [navigate]);

  // Code validation effect
  useEffect(() => {
    const validateCode = () => {
      if (!code.trim()) {
        setValidationStatus('ready');
        return;
      }

      // Simple validation logic
      if (code.includes('function') && code.includes('return')) {
        setValidationStatus('valid');
      } else if (code.includes('console.log') || code.includes('alert')) {
        setValidationStatus('warning');
      } else {
        setValidationStatus('invalid');
      }
    };

    const debounceTimer = setTimeout(validateCode, 500);
    return () => clearTimeout(debounceTimer);
  }, [code]);

  // Handlers
  const handleLanguageChange = (language) => {
    setSelectedLanguage(language);
    
    // Set default code for different languages
    const defaultCodes = {
      javascript: `// Welcome to PromptClash Battle Room!
// Solve the problem below and submit your solution

function solution() {
    // Your code here
    
}`,
      python: `# Welcome to PromptClash Battle Room!
# Solve the problem below and submit your solution

def solution():
    # Your code here
    pass`,
      java: `// Welcome to PromptClash Battle Room!
// Solve the problem below and submit your solution

public class Solution {
    public int[] solution() {
        // Your code here
        
    }
}`,
      cpp: `// Welcome to PromptClash Battle Room!
// Solve the problem below and submit your solution

#include <vector>
using namespace std;

class Solution {
public:
    vector<int> solution() {
        // Your code here
        
    }
};`,
      html: `<!DOCTYPE html>
<html>
<head>
    <title>PromptClash Solution</title>
</head>
<body>
    <!-- Your HTML here -->
    
</body>
</html>`,
      css: `/* Welcome to PromptClash Battle Room! */
/* Style your solution below */

.container {
    /* Your CSS here */
    
}`
    };
    
    setCode(defaultCodes[language] || '// Start coding here...');
  };

  const handleCodeSubmit = async () => {
    if (!code.trim()) return;
    
    setIsSubmitting(true);
    
    // Simulate submission
    setTimeout(() => {
      setIsSubmitting(false);
      // Navigate to results after submission
      navigate('/battle-results');
    }, 2000);
  };

  const handleExitBattle = () => {
    navigate('/battle-lobby');
  };

  const handleSendMessage = (message) => {
    const newMessage = {
      id: chatMessages.length + 1,
      senderId: currentUser.id,
      senderName: currentUser.name,
      content: message,
      timestamp: new Date()
    };
    setChatMessages(prev => [...prev, newMessage]);
  };

  // Generate preview content for HTML/JS
  const getPreviewContent = () => {
    if (selectedLanguage === 'html') {
      return code;
    } else if (selectedLanguage === 'javascript') {
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <title>JavaScript Preview</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .output { background: #f5f5f5; padding: 10px; border-radius: 4px; margin-top: 10px; }
          </style>
        </head>
        <body>
          <h3>JavaScript Output:</h3>
          <div class="output" id="output"></div>
          <script>
            try {
              ${code}
              document.getElementById('output').innerHTML = 'Code executed successfully';
            } catch (error) {
              document.getElementById('output').innerHTML = 'Error: ' + error.message;
            }
          </script>
        </body>
        </html>
      `;
    }
    return '<div>Preview not available for this language</div>';
  };

  const showPreview = ['html', 'javascript'].includes(selectedLanguage);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <BattleQuickJoin />
      
      <div className="pt-16 h-screen flex flex-col">
        {/* Battle Timer Header */}
        <BattleTimer
          timeRemaining={timeRemaining}
          totalTime={battleData.totalTime}
          battleStatus={battleData.status}
          participantCount={battleData.participantCount}
          onExitBattle={handleExitBattle}
          isSpectator={currentUser.isSpectator}
        />

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Prompt */}
          <div className={`transition-all duration-300 ${
            leftPanelCollapsed ? 'w-12' : 'w-[30%]'
          } min-w-[48px]`}>
            <PromptPanel
              prompt={promptData}
              isCollapsed={leftPanelCollapsed}
              onToggleCollapse={() => setLeftPanelCollapsed(!leftPanelCollapsed)}
            />
          </div>

          {/* Center Panel - Code Editor */}
          <div className={`transition-all duration-300 ${
            leftPanelCollapsed && rightPanelCollapsed ? 'w-[calc(100%-96px)]' :
            leftPanelCollapsed ? 'w-[calc(70%+18%)]': rightPanelCollapsed ?'w-[calc(50%+18%)]': 'w-[50%]'
          }`}>
            <CodeEditor
              language={selectedLanguage}
              onLanguageChange={handleLanguageChange}
              code={code}
              onCodeChange={setCode}
              onSubmit={handleCodeSubmit}
              isSubmitting={isSubmitting}
              validationStatus={validationStatus}
              fontSize={fontSize}
              onFontSizeChange={setFontSize}
            />
          </div>

          {/* Right Panel - Preview/Participants/Chat */}
          <div className={`transition-all duration-300 ${
            rightPanelCollapsed ? 'w-12' : 'w-[20%]'
          } min-w-[48px]`}>
            <RightPanel
              participants={participants}
              currentUser={currentUser}
              isCollapsed={rightPanelCollapsed}
              onToggleCollapse={() => setRightPanelCollapsed(!rightPanelCollapsed)}
              showPreview={showPreview}
              previewContent={getPreviewContent()}
              chatMessages={chatMessages}
              onSendMessage={handleSendMessage}
              isSpectator={currentUser.isSpectator}
            />
          </div>
        </div>
      </div>

      {/* Mobile Responsive Message */}
      <div className="md:hidden fixed inset-0 bg-background flex items-center justify-center p-6 z-[1001]">
        <div className="text-center">
          <div className="w-16 h-16 bg-warning/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-foreground mb-2">Desktop Required</h2>
          <p className="text-text-secondary mb-4">
            The Battle Room is optimized for desktop use. Please switch to a larger screen for the best coding experience.
          </p>
          <button
            onClick={() => navigate('/battle-lobby')}
            className="bg-primary text-primary-foreground px-6 py-2 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-150"
          >
            Back to Lobby
          </button>
        </div>
      </div>
    </div>
  );
};

export default BattleRoom;