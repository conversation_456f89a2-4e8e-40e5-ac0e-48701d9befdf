import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import Icon from '../../../components/AppIcon';

const StatisticsTab = ({ battleStats }) => {
  const COLORS = ['#00D9FF', '#7C3AED', '#10B981', '#F59E0B', '#EF4444'];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-popover border border-border rounded-lg p-3 shadow-elevation">
          <p className="text-sm font-medium text-foreground">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-8">
      {/* Overview Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Icon name="Users" size={24} color="var(--color-primary)" />
          </div>
          <div className="text-2xl font-bold text-foreground">{battleStats.totalParticipants}</div>
          <div className="text-sm text-text-secondary">Participants</div>
        </div>
        
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Icon name="Clock" size={24} color="var(--color-success)" />
          </div>
          <div className="text-2xl font-bold text-foreground">{battleStats.averageTime}s</div>
          <div className="text-sm text-text-secondary">Avg. Time</div>
        </div>
        
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Icon name="CheckCircle" size={24} color="var(--color-accent)" />
          </div>
          <div className="text-2xl font-bold text-foreground">{battleStats.completionRate}%</div>
          <div className="text-sm text-text-secondary">Completion</div>
        </div>
        
        <div className="bg-card border border-border rounded-lg p-4 text-center">
          <div className="flex items-center justify-center mb-2">
            <Icon name="Star" size={24} color="var(--color-warning)" />
          </div>
          <div className="text-2xl font-bold text-foreground">{battleStats.averageRating}</div>
          <div className="text-sm text-text-secondary">Avg. Rating</div>
        </div>
      </div>

      {/* Completion Rate Chart */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <Icon name="TrendingUp" size={20} className="mr-2" />
          Completion Timeline
        </h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={battleStats.completionTimeline}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
              <XAxis 
                dataKey="time" 
                stroke="var(--color-text-secondary)"
                fontSize={12}
              />
              <YAxis 
                stroke="var(--color-text-secondary)"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="submissions" 
                stroke="var(--color-primary)" 
                strokeWidth={2}
                dot={{ fill: 'var(--color-primary)', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Language Distribution */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <Icon name="Code" size={20} className="mr-2" />
            Language Distribution
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={battleStats.languageDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {battleStats.languageDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Execution Time Distribution */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <Icon name="Zap" size={20} className="mr-2" />
            Execution Time Distribution
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={battleStats.executionTimeDistribution}>
                <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                <XAxis 
                  dataKey="range" 
                  stroke="var(--color-text-secondary)"
                  fontSize={12}
                />
                <YAxis 
                  stroke="var(--color-text-secondary)"
                  fontSize={12}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="count" 
                  fill="var(--color-secondary)"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Difficulty Feedback */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <Icon name="BarChart3" size={20} className="mr-2" />
          Difficulty Rating Breakdown
        </h3>
        <div className="space-y-4">
          {battleStats.difficultyFeedback.map((item, index) => (
            <div key={index} className="flex items-center space-x-4">
              <div className="w-20 text-sm text-text-secondary">{item.rating} stars</div>
              <div className="flex-1 bg-muted rounded-full h-3 overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-primary to-secondary transition-all duration-500"
                  style={{ width: `${item.percentage}%` }}
                ></div>
              </div>
              <div className="w-16 text-sm text-foreground text-right">
                {item.count} ({item.percentage}%)
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <Icon name="Award" size={20} className="mr-2" />
          Top Performers
        </h3>
        <div className="space-y-3">
          {battleStats.topPerformers.map((performer, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  index === 0 ? 'bg-warning text-white' :
                  index === 1 ? 'bg-gray-400 text-white' :
                  index === 2 ? 'bg-amber-600 text-white': 'bg-muted text-text-secondary'
                }`}>
                  {index + 1}
                </div>
                <div>
                  <div className="font-medium text-foreground">{performer.username}</div>
                  <div className="text-sm text-text-secondary">Level {performer.level}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold text-primary">{performer.score} pts</div>
                <div className="text-sm text-text-secondary">{performer.time}ms</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default StatisticsTab;