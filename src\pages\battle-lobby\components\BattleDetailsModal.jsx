import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const BattleDetailsModal = ({ battle, isOpen, onClose, onJoin, onSpectate }) => {
  if (!isOpen || !battle) return null;

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-success bg-success/10';
      case 'medium':
        return 'text-warning bg-warning/10';
      case 'hard':
        return 'text-error bg-error/10';
      default:
        return 'text-text-secondary bg-muted';
    }
  };

  const formatTimeRemaining = (seconds) => {
    if (seconds <= 0) return 'Starting soon';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const participants = [
    { id: 1, name: "CodeMaster", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face", level: 15 },
    { id: 2, name: "AlgoQueen", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face", level: 12 },
    { id: 3, name: "DevNinja", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face", level: 18 }
  ];

  return (
    <div className="fixed inset-0 z-[1020] flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="relative bg-card border border-border rounded-xl shadow-elevation max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <h2 className="text-xl font-bold text-foreground">{battle.title}</h2>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(battle.difficulty)}`}>
              {battle.difficulty}
            </span>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Battle Info */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <Icon name="Users" size={24} className="mx-auto mb-2 text-primary" />
              <div className="text-lg font-bold text-foreground">{battle.participants}/{battle.maxParticipants}</div>
              <div className="text-xs text-text-secondary">Participants</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <Icon name="Eye" size={24} className="mx-auto mb-2 text-secondary" />
              <div className="text-lg font-bold text-foreground">{battle.spectators}</div>
              <div className="text-xs text-text-secondary">Spectators</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <Icon name="Clock" size={24} className="mx-auto mb-2 text-warning" />
              <div className="text-lg font-bold text-foreground">{formatTimeRemaining(battle.timeRemaining)}</div>
              <div className="text-xs text-text-secondary">Time Left</div>
            </div>
            <div className="text-center p-4 bg-muted/20 rounded-lg">
              <Icon name="Award" size={24} className="mx-auto mb-2 text-accent" />
              <div className="text-lg font-bold text-foreground">{battle.prize}</div>
              <div className="text-xs text-text-secondary">Prize Pool</div>
            </div>
          </div>

          {/* Prompt Preview */}
          <div>
            <h3 className="font-semibold text-foreground mb-3">Challenge Preview</h3>
            <div className="bg-muted/20 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Icon name="Tag" size={16} className="text-text-secondary" />
                <span className="text-sm text-text-secondary">{battle.category}</span>
                <span className="text-sm text-text-secondary">•</span>
                <span className="text-sm text-text-secondary">{battle.language}</span>
              </div>
              <p className="text-foreground">
                {battle.description || `Create a function that efficiently solves the given problem using optimal algorithms. \nYou'll have ${battle.duration} minutes to implement your solution with proper error handling and edge case coverage.`}
              </p>
            </div>
          </div>

          {/* Current Participants */}
          <div>
            <h3 className="font-semibold text-foreground mb-3">Current Participants</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {participants.map((participant) => (
                <div key={participant.id} className="flex items-center space-x-3 p-3 bg-muted/20 rounded-lg">
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-muted">
                    <img 
                      src={participant.avatar} 
                      alt={participant.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = '/assets/images/no_image.png';
                      }}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-foreground">{participant.name}</div>
                    <div className="text-sm text-text-secondary">Level {participant.level}</div>
                  </div>
                </div>
              ))}
              {battle.participants > participants.length && (
                <div className="flex items-center justify-center p-3 bg-muted/20 rounded-lg border-2 border-dashed border-border">
                  <span className="text-text-secondary">+{battle.participants - participants.length} more</span>
                </div>
              )}
            </div>
          </div>

          {/* Rules */}
          <div>
            <h3 className="font-semibold text-foreground mb-3">Battle Rules</h3>
            <ul className="space-y-2 text-sm text-text-secondary">
              <li className="flex items-start space-x-2">
                <Icon name="Check" size={16} className="text-success mt-0.5 flex-shrink-0" />
                <span>Complete the challenge within the time limit</span>
              </li>
              <li className="flex items-start space-x-2">
                <Icon name="Check" size={16} className="text-success mt-0.5 flex-shrink-0" />
                <span>Code must pass all test cases to be eligible for voting</span>
              </li>
              <li className="flex items-start space-x-2">
                <Icon name="Check" size={16} className="text-success mt-0.5 flex-shrink-0" />
                <span>Audience votes determine the winner based on code quality</span>
              </li>
              <li className="flex items-start space-x-2">
                <Icon name="X" size={16} className="text-error mt-0.5 flex-shrink-0" />
                <span>No external libraries or AI assistance allowed</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-3 p-6 border-t border-border">
          {battle.status === 'waiting' && battle.participants < battle.maxParticipants && (
            <Button
              variant="default"
              onClick={() => onJoin(battle)}
              className="flex-1"
              iconName="Zap"
              iconPosition="left"
            >
              Join Battle
            </Button>
          )}
          {battle.status === 'active' && (
            <Button
              variant="outline"
              onClick={() => onSpectate(battle)}
              className="flex-1"
              iconName="Eye"
              iconPosition="left"
            >
              Spectate Live
            </Button>
          )}
          <Button
            variant="ghost"
            onClick={onClose}
            className="px-6"
          >
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BattleDetailsModal;