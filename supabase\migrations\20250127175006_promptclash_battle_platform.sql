-- Location: supabase/migrations/20250127175006_promptclash_battle_platform.sql
-- Schema Analysis: Fresh project - no existing schema
-- Integration Type: Complete schema creation for PromptClash battle platform
-- Dependencies: None - initial migration

-- 1. Types and Enums
CREATE TYPE public.user_role AS ENUM ('admin', 'moderator', 'user');
CREATE TYPE public.battle_status AS ENUM ('waiting', 'active', 'voting', 'completed', 'cancelled');
CREATE TYPE public.prompt_difficulty AS ENUM ('easy', 'medium', 'hard');
CREATE TYPE public.submission_status AS ENUM ('draft', 'submitted', 'testing', 'passed', 'failed');
CREATE TYPE public.vote_type AS ENUM ('winner', 'creativity', 'efficiency');

-- 2. Core Tables
-- User profiles table (intermediary for auth relationships)
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    username TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    role public.user_role DEFAULT 'user'::public.user_role,
    xp INTEGER DEFAULT 0,
    win_streak INTEGER DEFAULT 0,
    total_wins INTEGER DEFAULT 0,
    total_battles INTEGER DEFAULT 0,
    github_username TEXT,
    bio TEXT,
    preferred_language TEXT DEFAULT 'javascript',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Prompts table
CREATE TABLE public.prompts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    difficulty public.prompt_difficulty NOT NULL,
    constraints TEXT[],
    input_format TEXT,
    output_format TEXT,
    examples JSONB,
    time_limit_minutes INTEGER DEFAULT 30,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Battles table
CREATE TABLE public.battles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    prompt_id UUID REFERENCES public.prompts(id) ON DELETE CASCADE,
    creator_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    status public.battle_status DEFAULT 'waiting'::public.battle_status,
    max_participants INTEGER DEFAULT 20,
    current_participants INTEGER DEFAULT 0,
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    voting_end_time TIMESTAMPTZ,
    time_limit_minutes INTEGER DEFAULT 30,
    is_public BOOLEAN DEFAULT true,
    room_code TEXT UNIQUE,
    winner_declarations JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Battle participants junction table
CREATE TABLE public.battle_participants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battle_id UUID REFERENCES public.battles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    joined_at TIMESTAMPZ DEFAULT CURRENT_TIMESTAMP,
    is_spectator BOOLEAN DEFAULT false,
    UNIQUE(battle_id, user_id)
);

-- Submissions table
CREATE TABLE public.submissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battle_id UUID REFERENCES public.battles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    language TEXT NOT NULL,
    code TEXT NOT NULL,
    status public.submission_status DEFAULT 'draft'::public.submission_status,
    execution_time_ms INTEGER,
    memory_used_mb DECIMAL(10,2),
    test_results JSONB,
    submitted_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(battle_id, user_id)
);

-- Votes table
CREATE TABLE public.votes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battle_id UUID REFERENCES public.battles(id) ON DELETE CASCADE,
    voter_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    voted_for_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    vote_type public.vote_type NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(battle_id, voter_id, vote_type)
);

-- Comments table
CREATE TABLE public.comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battle_id UUID REFERENCES public.battles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Chat messages table for real-time battle chat
CREATE TABLE public.chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    battle_id UUID REFERENCES public.battles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'message',
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 3. Essential Indexes
CREATE INDEX idx_user_profiles_username ON public.user_profiles(username);
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_prompts_category ON public.prompts(category);
CREATE INDEX idx_prompts_difficulty ON public.prompts(difficulty);
CREATE INDEX idx_battles_status ON public.battles(status);
CREATE INDEX idx_battles_creator_id ON public.battles(creator_id);
CREATE INDEX idx_battles_start_time ON public.battles(start_time);
CREATE INDEX idx_battle_participants_battle_id ON public.battle_participants(battle_id);
CREATE INDEX idx_battle_participants_user_id ON public.battle_participants(user_id);
CREATE INDEX idx_submissions_battle_id ON public.submissions(battle_id);
CREATE INDEX idx_submissions_user_id ON public.submissions(user_id);
CREATE INDEX idx_votes_battle_id ON public.votes(battle_id);
CREATE INDEX idx_comments_battle_id ON public.comments(battle_id);
CREATE INDEX idx_chat_messages_battle_id ON public.chat_messages(battle_id);

-- 4. Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prompts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battle_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- 5. Helper Functions for RLS
CREATE OR REPLACE FUNCTION public.is_battle_participant(battle_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.battle_participants bp
    WHERE bp.battle_id = battle_uuid AND bp.user_id = auth.uid()
)
$$;

CREATE OR REPLACE FUNCTION public.is_battle_creator(battle_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.battles b
    WHERE b.id = battle_uuid AND b.creator_id = auth.uid()
)
$$;

CREATE OR REPLACE FUNCTION public.can_access_battle(battle_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.battles b
    WHERE b.id = battle_uuid AND (
        b.is_public = true OR
        b.creator_id = auth.uid() OR
        public.is_battle_participant(battle_uuid)
    )
)
$$;

CREATE OR REPLACE FUNCTION public.has_role(required_role TEXT)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role::text = required_role
)
$$;

-- 6. RLS Policies
-- User profiles policies
CREATE POLICY "users_view_all_profiles" ON public.user_profiles
FOR SELECT TO authenticated USING (true);

CREATE POLICY "users_update_own_profile" ON public.user_profiles
FOR UPDATE TO authenticated USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- Prompts policies (public read, admin/moderator create)
CREATE POLICY "public_view_prompts" ON public.prompts
FOR SELECT TO authenticated USING (true);

CREATE POLICY "admin_manage_prompts" ON public.prompts
FOR ALL TO authenticated USING (public.has_role('admin') OR public.has_role('moderator'))
WITH CHECK (public.has_role('admin') OR public.has_role('moderator'));

-- Battles policies
CREATE POLICY "view_accessible_battles" ON public.battles
FOR SELECT TO authenticated USING (public.can_access_battle(id));

CREATE POLICY "creators_manage_battles" ON public.battles
FOR ALL TO authenticated USING (public.is_battle_creator(id))
WITH CHECK (auth.uid() = creator_id);

-- Battle participants policies
CREATE POLICY "participants_view_battle_members" ON public.battle_participants
FOR SELECT TO authenticated USING (public.can_access_battle(battle_id));

CREATE POLICY "users_join_battles" ON public.battle_participants
FOR INSERT TO authenticated WITH CHECK (auth.uid() = user_id);

CREATE POLICY "users_leave_battles" ON public.battle_participants
FOR DELETE TO authenticated USING (auth.uid() = user_id);

-- Submissions policies
CREATE POLICY "participants_view_submissions" ON public.submissions
FOR SELECT TO authenticated USING (public.is_battle_participant(battle_id));

CREATE POLICY "users_manage_own_submissions" ON public.submissions
FOR ALL TO authenticated USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Votes policies
CREATE POLICY "participants_vote" ON public.votes
FOR ALL TO authenticated USING (public.is_battle_participant(battle_id))
WITH CHECK (public.is_battle_participant(battle_id) AND auth.uid() = voter_id);

-- Comments policies
CREATE POLICY "participants_view_comments" ON public.comments
FOR SELECT TO authenticated USING (public.can_access_battle(battle_id));

CREATE POLICY "participants_create_comments" ON public.comments
FOR INSERT TO authenticated WITH CHECK (public.is_battle_participant(battle_id) AND auth.uid() = user_id);

CREATE POLICY "users_update_own_comments" ON public.comments
FOR UPDATE TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Chat messages policies
CREATE POLICY "participants_view_chat" ON public.chat_messages
FOR SELECT TO authenticated USING (public.can_access_battle(battle_id));

CREATE POLICY "participants_send_messages" ON public.chat_messages
FOR INSERT TO authenticated WITH CHECK (public.is_battle_participant(battle_id) AND auth.uid() = user_id);

-- 7. Triggers for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
DECLARE
    username_base TEXT;
    username_candidate TEXT;
    username_counter INTEGER := 1;
BEGIN
    -- Generate base username from email
    username_base := split_part(NEW.email, '@', 1);
    username_candidate := username_base;
    
    -- Ensure username uniqueness
    WHILE EXISTS (SELECT 1 FROM public.user_profiles WHERE username = username_candidate) LOOP
        username_candidate := username_base || username_counter::text;
        username_counter := username_counter + 1;
    END LOOP;
    
    INSERT INTO public.user_profiles (
        id, 
        email, 
        username, 
        full_name, 
        role,
        avatar_url
    ) VALUES (
        NEW.id, 
        NEW.email, 
        username_candidate,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE((NEW.raw_user_meta_data->>'role')::public.user_role, 'user'::public.user_role),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    
    RETURN NEW;
END;
$$;

CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 8. Utility Functions
CREATE OR REPLACE FUNCTION public.generate_room_code()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER := 0;
BEGIN
    FOR i IN 1..6 LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    RETURN result;
END;
$$;

-- 9. Mock Data
DO $$
DECLARE
    admin_uuid UUID := gen_random_uuid();
    user1_uuid UUID := gen_random_uuid();
    user2_uuid UUID := gen_random_uuid();
    prompt1_uuid UUID := gen_random_uuid();
    prompt2_uuid UUID := gen_random_uuid();
    battle1_uuid UUID := gen_random_uuid();
    battle2_uuid UUID := gen_random_uuid();
BEGIN
    -- Create auth users with all required fields
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (admin_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('password123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Admin User", "role": "admin"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (user1_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('password123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Alex Chen", "role": "user"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (user2_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('password123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Sarah Johnson", "role": "user"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Create sample prompts
    INSERT INTO public.prompts (id, title, description, category, difficulty, constraints, input_format, output_format, examples, time_limit_minutes, created_by) VALUES
        (prompt1_uuid, 'Two Sum Problem', 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice.', 'Arrays & Hashing', 'easy'::public.prompt_difficulty, 
         ARRAY['2 ≤ nums.length ≤ 10⁴', '-10⁹ ≤ nums[i] ≤ 10⁹', '-10⁹ ≤ target ≤ 10⁹', 'Only one valid answer exists'],
         'The first line contains the array of integers. The second line contains the target integer.',
         'Return an array of two indices that add up to the target.',
         '[{"input": "nums = [2,7,11,15], target = 9", "output": "[0,1]", "explanation": "Because nums[0] + nums[1] == 9, we return [0, 1]."}]'::jsonb,
         30, admin_uuid),
        (prompt2_uuid, 'Valid Parentheses', 'Given a string s containing just the characters ''('', '')'', ''{'', ''}'', ''['' and '']'', determine if the input string is valid. An input string is valid if: Open brackets must be closed by the same type of brackets. Open brackets must be closed in the correct order.', 'Stack', 'easy'::public.prompt_difficulty,
         ARRAY['1 ≤ s.length ≤ 10⁴', 's consists of parentheses only ''()[]{}'''],
         'A string containing only brackets',
         'Return true if valid, false otherwise',
         '[{"input": "s = \"()\"", "output": "true", "explanation": "Valid parentheses"}, {"input": "s = \"()[]{}\"", "output": "true", "explanation": "All brackets properly closed"}]'::jsonb,
         20, admin_uuid);

    -- Create sample battles
    INSERT INTO public.battles (id, title, prompt_id, creator_id, status, max_participants, current_participants, start_time, end_time, time_limit_minutes, room_code) VALUES
        (battle1_uuid, 'Algorithm Sprint Challenge', prompt1_uuid, admin_uuid, 'active'::public.battle_status, 20, 3, 
         now() - interval '10 minutes', now() + interval '20 minutes', 30, 'ABC123'),
        (battle2_uuid, 'Stack Masters Showdown', prompt2_uuid, user1_uuid, 'waiting'::public.battle_status, 15, 1,
         now() + interval '1 hour', now() + interval '1 hour 20 minutes', 20, 'XYZ789');

    -- Add battle participants
    INSERT INTO public.battle_participants (battle_id, user_id, joined_at, is_spectator) VALUES
        (battle1_uuid, admin_uuid, now() - interval '10 minutes', false),
        (battle1_uuid, user1_uuid, now() - interval '8 minutes', false),
        (battle1_uuid, user2_uuid, now() - interval '5 minutes', false),
        (battle2_uuid, user1_uuid, now() - interval '30 minutes', false);

    -- Add sample submissions
    INSERT INTO public.submissions (battle_id, user_id, language, code, status, submitted_at) VALUES
        (battle1_uuid, user2_uuid, 'javascript', 
         'function twoSum(nums, target) { const map = new Map(); for (let i = 0; i < nums.length; i++) { const complement = target - nums[i]; if (map.has(complement)) { return [map.get(complement), i]; } map.set(nums[i], i); } return []; }',
         'submitted'::public.submission_status, now() - interval '2 minutes');

    -- Add sample chat messages
    INSERT INTO public.chat_messages (battle_id, user_id, content, created_at) VALUES
        (battle1_uuid, user1_uuid, 'Good luck everyone! 🚀', now() - interval '5 minutes'),
        (battle1_uuid, user2_uuid, 'This problem looks tricky but doable', now() - interval '4 minutes'),
        (battle1_uuid, admin_uuid, 'Remember to consider edge cases!', now() - interval '3 minutes'),
        (battle1_uuid, user1_uuid, 'Anyone else thinking hash map approach?', now() - interval '2 minutes');

EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Mock data creation error: %', SQLERRM;
END $$;