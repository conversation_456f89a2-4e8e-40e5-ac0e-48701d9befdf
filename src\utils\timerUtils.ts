export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export const getTimeRemaining = (startTime: Date, timeLimit: number): number => {
  const now = new Date().getTime();
  const start = startTime.getTime();
  const elapsed = Math.floor((now - start) / 1000);
  return Math.max(0, timeLimit - elapsed);
};

export const isTimeWarning = (timeRemaining: number, warningThreshold: number = 30): boolean => {
  return timeRemaining <= warningThreshold;
};

export const isTimeCritical = (timeRemaining: number, criticalThreshold: number = 10): boolean => {
  return timeRemaining <= criticalThreshold;
};

export const getTimerColor = (timeRemaining: number): string => {
  if (timeRemaining <= 10) return 'text-red-500';
  if (timeRemaining <= 30) return 'text-yellow-500';
  return 'text-green-500';
};

export const getProgressPercentage = (timeRemaining: number, totalTime: number): number => {
  return ((timeRemaining / totalTime) * 100);
}; 