import { useState, useEffect } from 'react'

interface LivePreviewProps {
  code: string
}

const LivePreview = ({ code }: LivePreviewProps) => {
  const [output, setOutput] = useState('')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    try {
      // Simple code execution for demo purposes
      // In a real app, you'd want to use a sandboxed environment
      if (code.includes('console.log')) {
        const logs: string[] = []
        const originalLog = console.log
        console.log = (...args) => {
          logs.push(args.join(' '))
        }
        
        // Try to execute the code
        try {
          eval(code)
          setOutput(logs.join('\n'))
          setError(null)
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Execution error')
          setOutput('')
        }
        
        console.log = originalLog
      } else {
        setOutput('Add console.log() statements to see output')
        setError(null)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Code error')
      setOutput('')
    }
  }, [code])

  return (
    <div className="bg-gray-900 rounded-lg p-4 h-64 overflow-auto">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-sm font-semibold text-gray-300">Output</h4>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-xs text-gray-400">Live</span>
        </div>
      </div>
      
      {error ? (
        <div className="text-red-400 text-sm font-mono">
          <div className="mb-2">❌ Error:</div>
          <pre className="whitespace-pre-wrap">{error}</pre>
        </div>
      ) : (
        <div className="text-green-400 text-sm font-mono">
          <pre className="whitespace-pre-wrap">{output || 'No output yet...'}</pre>
        </div>
      )}
      
      <div className="mt-4 text-xs text-gray-500">
        <p>💡 Tip: Use console.log() to see your output</p>
      </div>
    </div>
  )
}

export default LivePreview 