import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';

const AuthHeader = () => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
      <div className="flex items-center justify-between h-16 px-6 max-w-7xl mx-auto">
        {/* Logo */}
        <Link to="/battle-lobby" className="flex items-center space-x-2 hover:opacity-80 transition-opacity duration-150">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-lg flex items-center justify-center">
            <Icon name="Code" size={20} color="white" />
          </div>
          <span className="text-xl font-bold text-primary">PromptClash</span>
        </Link>

        {/* Language Selector */}
        <div className="flex items-center space-x-2">
          <Icon name="Globe" size={18} className="text-text-secondary" />
          <span className="text-sm text-text-secondary">EN</span>
        </div>
      </div>
    </header>
  );
};

export default AuthHeader;