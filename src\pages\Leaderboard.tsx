import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';

// Mock data - in real app, this would come from Firebase
const mockUsers = [
  {
    id: '1',
    username: 'CodeMaster',
    avatar_url: 'https://via.placeholder.com/40',
    xp: 12500,
    win_streak: 15,
    total_battles: 89,
    wins: 67,
    losses: 22,
  },
  {
    id: '2',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    avatar_url: 'https://via.placeholder.com/40',
    xp: 11200,
    win_streak: 8,
    total_battles: 76,
    wins: 58,
    losses: 18,
  },
  {
    id: '3',
    username: 'AlgoWizard',
    avatar_url: 'https://via.placeholder.com/40',
    xp: 9800,
    win_streak: 12,
    total_battles: 65,
    wins: 52,
    losses: 13,
  },
  {
    id: '4',
    username: 'FrontendPro',
    avatar_url: 'https://via.placeholder.com/40',
    xp: 8900,
    win_streak: 6,
    total_battles: 72,
    wins: 48,
    losses: 24,
  },
  {
    id: '5',
    username: '<PERSON><PERSON><PERSON><PERSON>',
    avatar_url: 'https://via.placeholder.com/40',
    xp: 8200,
    win_streak: 9,
    total_battles: 58,
    wins: 45,
    losses: 13,
  },
];

const Leaderboard: React.FC = () => {
  const [users] = useState(mockUsers);
  const [sortBy, setSortBy] = useState<'xp' | 'wins' | 'win_streak'>('xp');

  const sortedUsers = [...users].sort((a, b) => b[sortBy] - a[sortBy]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return '🥇';
      case 2: return '🥈';
      case 3: return '🥉';
      default: return `#${rank}`;
    }
  };

  const getWinRate = (wins: number, total: number) => {
    return total > 0 ? ((wins / total) * 100).toFixed(1) : '0.0';
  };

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link to="/" className="text-3xl font-bold text-gradient">
                PromptClash
              </Link>
            </div>
            
            <nav className="flex items-center space-x-6">
              <Link to="/" className="text-gray-300 hover:text-white transition-colors">
                Home
              </Link>
              <span className="text-white font-semibold">Leaderboard</span>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">🏆 Leaderboard</h1>
          <p className="text-xl text-gray-400">
            Top coders competing in PromptClash battles
          </p>
        </div>

        {/* Sort Controls */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-800 rounded-lg p-1 flex">
            <button
              onClick={() => setSortBy('xp')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                sortBy === 'xp'
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              XP
            </button>
            <button
              onClick={() => setSortBy('wins')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                sortBy === 'wins'
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Wins
            </button>
            <button
              onClick={() => setSortBy('win_streak')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                sortBy === 'win_streak'
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              Win Streak
            </button>
          </div>
        </div>

        {/* Leaderboard Table */}
        <div className="card">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-4 px-6 text-gray-400 font-medium">Rank</th>
                  <th className="text-left py-4 px-6 text-gray-400 font-medium">Player</th>
                  <th className="text-right py-4 px-6 text-gray-400 font-medium">XP</th>
                  <th className="text-right py-4 px-6 text-gray-400 font-medium">Wins</th>
                  <th className="text-right py-4 px-6 text-gray-400 font-medium">Win Rate</th>
                  <th className="text-right py-4 px-6 text-gray-400 font-medium">Win Streak</th>
                  <th className="text-right py-4 px-6 text-gray-400 font-medium">Total Battles</th>
                </tr>
              </thead>
              <tbody>
                {sortedUsers.map((user, index) => (
                  <tr 
                    key={user.id} 
                    className="border-b border-gray-700 hover:bg-gray-700 transition-colors"
                  >
                    <td className="py-4 px-6">
                      <span className="text-2xl font-bold">
                        {getRankIcon(index + 1)}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <div className="flex items-center space-x-3">
                        <img 
                          src={user.avatar_url} 
                          alt={user.username}
                          className="w-10 h-10 rounded-full"
                        />
                        <div>
                          <div className="font-semibold text-white">
                            {user.username}
                          </div>
                          <div className="text-sm text-gray-400">
                            ID: {user.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-6 text-right">
                      <span className="font-bold text-primary-400">
                        {user.xp.toLocaleString()}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-right">
                      <span className="font-semibold text-green-400">
                        {user.wins}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-right">
                      <span className="text-gray-300">
                        {getWinRate(user.wins, user.total_battles)}%
                      </span>
                    </td>
                    <td className="py-4 px-6 text-right">
                      <span className="font-semibold text-yellow-400">
                        {user.win_streak}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-right">
                      <span className="text-gray-300">
                        {user.total_battles}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Stats Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
          <div className="card text-center">
            <div className="text-3xl font-bold text-primary-400">
              {users.length}
            </div>
            <div className="text-gray-400">Total Players</div>
          </div>
          <div className="card text-center">
            <div className="text-3xl font-bold text-green-400">
              {users.reduce((sum, user) => sum + user.wins, 0)}
            </div>
            <div className="text-gray-400">Total Wins</div>
          </div>
          <div className="card text-center">
            <div className="text-3xl font-bold text-yellow-400">
              {Math.max(...users.map(u => u.win_streak))}
            </div>
            <div className="text-gray-400">Best Streak</div>
          </div>
          <div className="card text-center">
            <div className="text-3xl font-bold text-purple-400">
              {users.reduce((sum, user) => sum + user.total_battles, 0)}
            </div>
            <div className="text-gray-400">Total Battles</div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Leaderboard; 