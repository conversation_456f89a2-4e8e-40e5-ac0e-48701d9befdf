import React from 'react';
import { useBattle } from '../context/BattleContext';
import { useAuth } from '../context/AuthContext';
import BattleEditor from '../components/BattleEditor';
import Timer from '../components/Timer';
import PromptCard from '../components/PromptCard';
import LivePreview from '../components/LivePreview';
import VotePanel from '../components/VotePanel';
import ResultCard from '../components/ResultCard';

const BattleRoom: React.FC = () => {
  const { 
    currentBattle, 
    timeRemaining, 
    isLoading, 
    error,
    startBattle,
    endBattle 
  } = useBattle();
  const { user } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading battle...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-red-400 text-xl">Error: {error}</div>
      </div>
    );
  }

  if (!currentBattle) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white text-xl">Battle not found</div>
      </div>
    );
  }

  const isPlayer1 = currentBattle.player1_id === user?.id;
  const isActive = currentBattle.status === 'active';
  const isVoting = currentBattle.status === 'voting';
  const isCompleted = currentBattle.status === 'completed';

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gradient">PromptClash</h1>
            <div className="text-gray-400">Battle #{currentBattle.id.slice(0, 8)}</div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-400">
              {isPlayer1 ? 'Player 1' : 'Player 2'}
            </div>
            {user?.avatar_url && (
              <img 
                src={user.avatar_url} 
                alt={user.username}
                className="w-8 h-8 rounded-full"
              />
            )}
          </div>
        </div>
      </header>

      {/* Battle Interface */}
      <div className="battle-container">
        {/* Left Side - Editor */}
        <div className="flex flex-col h-full">
          {/* Prompt Card */}
          <div className="mb-4">
            <PromptCard prompt={currentBattle.prompt} />
          </div>
          
          {/* Timer */}
          <div className="mb-4">
            <Timer 
              timeRemaining={timeRemaining}
              totalTime={currentBattle.time_limit}
              isActive={isActive}
            />
          </div>
          
          {/* Code Editor */}
          <div className="flex-1">
            <BattleEditor 
              language="javascript"
              readOnly={!isActive}
            />
          </div>
        </div>

        {/* Right Side - Preview & Results */}
        <div className="flex flex-col h-full">
          {isCompleted ? (
            <ResultCard battle={currentBattle} />
          ) : isVoting ? (
            <VotePanel battle={currentBattle} />
          ) : (
            <LivePreview 
              code={isPlayer1 ? currentBattle.code1 : currentBattle.code2}
            />
          )}
        </div>
      </div>

      {/* Battle Controls */}
      {currentBattle.status === 'waiting' && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2">
          <button 
            onClick={startBattle}
            className="btn-primary text-lg px-8 py-3"
          >
            Start Battle
          </button>
        </div>
      )}

      {isActive && timeRemaining <= 30 && (
        <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2">
          <button 
            onClick={endBattle}
            className="btn-danger text-lg px-8 py-3"
          >
            End Battle Early
          </button>
        </div>
      )}
    </div>
  );
};

export default BattleRoom; 