import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RightPanel = ({ 
  participants, 
  currentUser, 
  isCollapsed, 
  onToggleCollapse, 
  showPreview, 
  previewContent,
  chatMessages,
  onSendMessage,
  isSpectator 
}) => {
  const [activeTab, setActiveTab] = useState('participants');
  const [newMessage, setNewMessage] = useState('');

  const tabs = [
    { id: 'participants', label: 'Players', icon: 'Users', count: participants.length },
    { id: 'preview', label: 'Preview', icon: 'Eye' },
    { id: 'chat', label: 'Chat', icon: 'MessageCircle', count: chatMessages.length }
  ];

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      onSendMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className={`bg-card border-l border-border transition-all duration-300 ${isCollapsed ? 'w-12' : 'w-full'} h-full flex flex-col`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!isCollapsed && (
          <div className="flex space-x-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-1 px-3 py-1.5 rounded text-sm font-medium transition-colors duration-150 ${
                  activeTab === tab.id
                    ? 'bg-primary text-primary-foreground'
                    : 'text-text-secondary hover:text-foreground hover:bg-muted'
                }`}
              >
                <Icon name={tab.icon} size={14} />
                <span>{tab.label}</span>
                {tab.count !== undefined && (
                  <span className="bg-muted text-xs px-1.5 py-0.5 rounded-full">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </div>
        )}
        <button
          onClick={onToggleCollapse}
          className="p-1 hover:bg-muted rounded transition-colors duration-150"
        >
          <Icon name={isCollapsed ? "ChevronLeft" : "ChevronRight"} size={16} />
        </button>
      </div>

      {!isCollapsed && (
        <div className="flex-1 overflow-hidden">
          {/* Participants Tab */}
          {activeTab === 'participants' && (
            <div className="h-full overflow-y-auto p-4">
              <div className="space-y-3">
                {participants.map((participant) => (
                  <div
                    key={participant.id}
                    className={`flex items-center space-x-3 p-3 rounded-lg border ${
                      participant.id === currentUser.id
                        ? 'border-primary bg-primary/5' :'border-border bg-muted/50'
                    }`}
                  >
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-white">
                          {participant.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-card ${
                        participant.status === 'coding' ? 'bg-warning' :
                        participant.status === 'submitted' ? 'bg-success' :
                        participant.status === 'idle' ? 'bg-muted' : 'bg-error'
                      }`}></div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-foreground truncate">
                          {participant.name}
                        </span>
                        {participant.id === currentUser.id && (
                          <span className="text-xs text-primary font-medium">(You)</span>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-xs text-text-secondary">
                        <span>Level {participant.level}</span>
                        <span>•</span>
                        <span className="capitalize">{participant.status}</span>
                      </div>
                      
                      {/* Progress Bar */}
                      <div className="mt-2">
                        <div className="w-full bg-muted rounded-full h-1.5">
                          <div 
                            className={`h-1.5 rounded-full transition-all duration-300 ${
                              participant.status === 'submitted' ? 'bg-success' :
                              participant.status === 'coding' ? 'bg-warning' : 'bg-muted'
                            }`}
                            style={{ width: `${participant.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Preview Tab */}
          {activeTab === 'preview' && (
            <div className="h-full flex flex-col">
              <div className="p-4 border-b border-border">
                <h3 className="font-medium text-foreground">Live Preview</h3>
                <p className="text-xs text-text-secondary mt-1">
                  {showPreview ? 'HTML/JS output will appear here' : 'Preview not available for this language'}
                </p>
              </div>
              
              <div className="flex-1 overflow-hidden">
                {showPreview ? (
                  <iframe
                    srcDoc={previewContent}
                    className="w-full h-full border-none bg-white"
                    title="Code Preview"
                    sandbox="allow-scripts"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-text-secondary">
                    <div className="text-center">
                      <Icon name="EyeOff" size={48} className="mx-auto mb-2 opacity-50" />
                      <p className="text-sm">Preview not available</p>
                      <p className="text-xs mt-1">Switch to HTML/JS for live preview</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Chat Tab */}
          {activeTab === 'chat' && (
            <div className="h-full flex flex-col">
              {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex space-x-2 ${
                      message.senderId === currentUser.id ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.senderId !== currentUser.id && (
                      <div className="w-6 h-6 bg-gradient-to-br from-accent to-primary rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-xs font-bold text-white">
                          {message.senderName.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                    )}
                    
                    <div className={`max-w-[80%] ${
                      message.senderId === currentUser.id
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted text-foreground'
                    } rounded-lg px-3 py-2`}>
                      {message.senderId !== currentUser.id && (
                        <div className="text-xs font-medium mb-1">{message.senderName}</div>
                      )}
                      <div className="text-sm">{message.content}</div>
                      <div className="text-xs opacity-70 mt-1">
                        {new Date(message.timestamp).toLocaleTimeString([], { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Chat Input */}
              {!isSpectator && (
                <div className="p-4 border-t border-border">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type a message..."
                      className="flex-1 bg-background border border-border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
                      maxLength={200}
                    />
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                    >
                      <Icon name="Send" size={16} />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default RightPanel;