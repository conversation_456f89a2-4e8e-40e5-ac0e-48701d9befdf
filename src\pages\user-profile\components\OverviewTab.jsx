import React from 'react';
import Icon from '../../../components/AppIcon';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON>Chart, Pie, Cell } from 'recharts';

const OverviewTab = ({ user, achievements, skillRatings, recentActivity, performanceData }) => {
  const COLORS = ['#00D9FF', '#7C3AED', '#10B981', '#F59E0B', '#EF4444'];

  const ActivityItem = ({ activity }) => {
    const getActivityIcon = (type) => {
      switch (type) {
        case 'battle_won': return 'Trophy';
        case 'battle_lost': return 'X';
        case 'level_up': return 'TrendingUp';
        case 'achievement': return 'Award';
        default: return 'Activity';
      }
    };

    const getActivityColor = (type) => {
      switch (type) {
        case 'battle_won': return 'text-success';
        case 'battle_lost': return 'text-error';
        case 'level_up': return 'text-primary';
        case 'achievement': return 'text-warning';
        default: return 'text-text-secondary';
      }
    };

    return (
      <div className="flex items-start space-x-3 p-3 hover:bg-muted/50 rounded-lg transition-colors duration-150">
        <div className={`p-2 rounded-full bg-background ${getActivityColor(activity.type)}`}>
          <Icon name={getActivityIcon(activity.type)} size={16} />
        </div>
        <div className="flex-1 min-w-0">
          <p className="text-sm text-foreground">{activity.description}</p>
          <p className="text-xs text-text-secondary">{activity.timestamp}</p>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Achievements Section */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="Award" size={20} />
          <span>Achievements</span>
        </h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {achievements.map((achievement) => (
            <div
              key={achievement.id}
              className={`relative p-4 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${
                achievement.unlocked
                  ? 'border-primary bg-primary/5 hover:bg-primary/10' :'border-border bg-muted/20 opacity-60'
              }`}
            >
              <div className="text-center">
                <div className={`text-2xl mb-2 ${achievement.unlocked ? 'text-primary' : 'text-text-secondary'}`}>
                  {achievement.icon}
                </div>
                <h4 className="text-sm font-medium text-foreground mb-1">{achievement.name}</h4>
                <p className="text-xs text-text-secondary">{achievement.description}</p>
              </div>
              {achievement.unlocked && (
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-success rounded-full flex items-center justify-center">
                  <Icon name="Check" size={10} color="white" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Skill Ratings */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
            <Icon name="Target" size={20} />
            <span>Skill Ratings</span>
          </h3>
          <div className="space-y-4">
            {skillRatings.map((skill, index) => (
              <div key={skill.category}>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-foreground">{skill.category}</span>
                  <span className="text-sm font-bold" style={{ color: COLORS[index % COLORS.length] }}>
                    {skill.rating}
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-500"
                    style={{
                      width: `${(skill.rating / 2000) * 100}%`,
                      backgroundColor: COLORS[index % COLORS.length]
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
            <Icon name="Activity" size={20} />
            <span>Recent Activity</span>
          </h3>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {recentActivity.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))}
          </div>
        </div>
      </div>

      {/* Performance Charts */}
      <div className="grid lg:grid-cols-2 gap-6">
        {/* XP Progress Chart */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
            <Icon name="TrendingUp" size={20} />
            <span>XP Progress</span>
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={performanceData.xpProgress}>
                <CartesianGrid strokeDasharray="3 3" stroke="var(--color-border)" />
                <XAxis 
                  dataKey="date" 
                  stroke="var(--color-text-secondary)"
                  fontSize={12}
                />
                <YAxis 
                  stroke="var(--color-text-secondary)"
                  fontSize={12}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'var(--color-popover)',
                    border: '1px solid var(--color-border)',
                    borderRadius: '8px'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="xp" 
                  stroke="var(--color-primary)" 
                  strokeWidth={2}
                  dot={{ fill: 'var(--color-primary)', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Battle Results Distribution */}
        <div className="bg-card border border-border rounded-xl p-6">
          <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
            <Icon name="PieChart" size={20} />
            <span>Battle Results</span>
          </h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={performanceData.battleResults}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {performanceData.battleResults.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'var(--color-popover)',
                    border: '1px solid var(--color-border)',
                    borderRadius: '8px'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
          <div className="flex justify-center space-x-4 mt-4">
            {performanceData.battleResults.map((entry, index) => (
              <div key={entry.name} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                ></div>
                <span className="text-sm text-text-secondary">{entry.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverviewTab;