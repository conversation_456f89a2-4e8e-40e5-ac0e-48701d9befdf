# 🔥 Firebase Setup Guide for PromptClash

## ✅ **Step-by-Step Setup Instructions**

### 1. **Enable Google Authentication**
1. Go to: https://console.firebase.google.com/project/vibecodefeed/authentication/providers
2. Click on **"Sign-in method"** tab
3. Click on **"Google"** provider
4. Click **"Enable"**
5. Add your **project support email** (your email)
6. Click **"Save"**

### 2. **Create Firestore Database**
1. Go to: https://console.firebase.google.com/project/vibecodefeed/firestore
2. Click **"Create database"**
3. Choose **"Start in test mode"** (we'll add security rules later)
4. Choose a **location** (pick the closest to you)
5. Click **"Done"**

### 3. **Create Realtime Database**
1. Go to: https://console.firebase.google.com/project/vibecodefeed/realtime-database
2. Click **"Create database"**
3. Choose **"Start in test mode"** (we'll add security rules later)
4. Choose a **location** (pick the closest to you)
5. Click **"Done"**

### 4. **Apply Firestore Security Rules**
1. Go to: https://console.firebase.google.com/project/vibecodefeed/firestore/rules
2. Replace the existing rules with the content from `firestore.rules` file
3. Click **"Publish"**

### 5. **Apply Realtime Database Security Rules**
1. Go to: https://console.firebase.google.com/project/vibecodefeed/realtime-database/rules
2. Replace the existing rules with the content from `database.rules.json` file
3. Click **"Publish"**

### 6. **Add Sample Prompts (Optional)**
1. Go to: https://console.firebase.google.com/project/vibecodefeed/firestore
2. Click **"Start collection"**
3. Collection ID: `prompts`
4. Add documents with auto-generated IDs
5. Use the sample data from `setup-sample-data.js`

## 🚀 **Test Your Setup**

1. **Open your app:** http://localhost:3001/
2. **Click "Sign In to Battle"**
3. **Sign in with Google**
4. **Try creating a prompt or joining a battle**

## 📁 **Files Created**
- `firestore.rules` - Firestore security rules
- `database.rules.json` - Realtime Database security rules
- `setup-sample-data.js` - Sample prompts data
- `.env` - Your Firebase configuration

## 🔧 **Troubleshooting**

### If Google Sign-in doesn't work:
- Make sure Google Authentication is enabled
- Check that your domain is authorized (localhost should work for development)

### If database operations fail:
- Verify Firestore and Realtime Database are created
- Check that security rules are published
- Ensure you're signed in to the app

### If you see console errors:
- Check browser console for specific error messages
- Verify all Firebase services are properly configured

## 🎉 **You're Ready!**

Once you complete these steps, your PromptClash application will have:
- ✅ Google Authentication
- ✅ Real-time code synchronization
- ✅ User profiles and battle history
- ✅ Prompt creation and management
- ✅ Voting system
- ✅ Leaderboard functionality

**Happy coding! 🚀** 