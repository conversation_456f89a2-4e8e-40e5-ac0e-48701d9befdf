import { Prompt } from '../types'

export const generateSamplePrompts = (): Omit<Prompt, 'id' | 'created_at'>[] => {
  return [
    {
      title: 'Build a Todo App',
      text: 'Create a simple todo application with the following features:\n- Add new todos\n- Mark todos as complete\n- Delete todos\n- Filter todos (all, active, completed)\n- Local storage persistence\n\nUse vanilla JavaScript, HTML, and CSS.',
      category: 'JavaScript',
      difficulty: 'easy',
      author_id: 'system',
      votes: 0
    },
    {
      title: 'Create a Weather Dashboard',
      text: 'Build a weather dashboard that displays current weather information for a given city. Include:\n- Current temperature, humidity, and wind speed\n- 5-day forecast\n- Weather icons\n- Search functionality\n- Responsive design\n\nUse any weather API of your choice.',
      category: 'React',
      difficulty: 'medium',
      author_id: 'system',
      votes: 0
    },
    {
      title: 'Design a Chat Interface',
      text: 'Create a modern chat interface with the following features:\n- Message bubbles with timestamps\n- User avatars\n- Typing indicators\n- Message status (sent, delivered, read)\n- Emoji support\n- Responsive design\n\nFocus on the UI/UX design.',
      category: 'CSS',
      difficulty: 'hard',
      author_id: 'system',
      votes: 0
    },
    {
      title: 'Build a Calculator',
      text: 'Create a fully functional calculator with:\n- Basic arithmetic operations (+, -, *, /)\n- Clear and delete functions\n- Decimal point support\n- Keyboard input support\n- Error handling for division by zero\n- Responsive design',
      category: 'JavaScript',
      difficulty: 'easy',
      author_id: 'system',
      votes: 0
    },
    {
      title: 'Create a Portfolio Site',
      text: 'Design and build a personal portfolio website with:\n- Hero section with introduction\n- About section\n- Skills showcase\n- Project gallery\n- Contact form\n- Smooth scrolling navigation\n- Mobile-responsive design',
      category: 'React',
      difficulty: 'medium',
      author_id: 'system',
      votes: 0
    },
    {
      title: 'Design a Game UI',
      text: 'Create a game user interface for a fantasy RPG with:\n- Health and mana bars\n- Inventory system\n- Character stats panel\n- Mini-map\n- Quest log\n- Settings menu\n- Animated elements',
      category: 'CSS',
      difficulty: 'hard',
      author_id: 'system',
      votes: 0
    },
    {
      title: 'Build a Shopping Cart',
      text: 'Create an e-commerce shopping cart with:\n- Product listing with images and prices\n- Add/remove items functionality\n- Quantity controls\n- Price calculations\n- Local storage persistence\n- Checkout form\n- Responsive design',
      category: 'JavaScript',
      difficulty: 'medium',
      author_id: 'system',
      votes: 0
    }
  ]
}

export const getRandomPrompt = (): Omit<Prompt, 'id' | 'created_at'> => {
  const prompts = generateSamplePrompts()
  const randomIndex = Math.floor(Math.random() * prompts.length)
  return prompts[randomIndex]
} 