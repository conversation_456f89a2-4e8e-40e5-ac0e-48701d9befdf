import React, { useState, useEffect, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import Header from '../../components/ui/Header';
import BattleQuickJoin from '../../components/ui/BattleQuickJoin';
import LeaderboardTabs from './components/LeaderboardTabs';
import SearchBar from './components/SearchBar';
import FilterOptions from './components/FilterOptions';
import UserRankCard from './components/UserRankCard';
import CurrentUserPosition from './components/CurrentUserPosition';
import AchievementShowcase from './components/AchievementShowcase';
import LoadingState from './components/LoadingState';
import EmptyState from './components/EmptyState';

const Leaderboard = () => {
  const [activeTab, setActiveTab] = useState('global');
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [difficultyFilter, setDifficultyFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Mock current user data
  const currentUser = {
    id: 'user_123',
    username: 'CodeWarrior2024',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    xp: 15750,
    level: 18,
    winRate: 73,
    streak: 8,
    battlesWon: 142,
    avgSolveTime: 245,
    favoriteLanguage: 'JavaScript',
    positionChange: 3,
    isOnline: true,
    recentActivity: 'Won Algorithm Sprint battle',
    specialization: 'Frontend Development'
  };

  // Mock leaderboard data
  const mockUsers = [
    {
      id: 'user_001',
      username: 'AlgoMaster',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      xp: 28450,
      level: 25,
      winRate: 89,
      streak: 15,
      battlesWon: 287,
      avgSolveTime: 180,
      favoriteLanguage: 'Python',
      positionChange: 0,
      isOnline: true,
      isFollowing: false,
      recentActivity: 'Won Expert Algorithm Challenge',
      specialization: 'Machine Learning',
      badges: [
        { icon: 'Crown', color: 'text-yellow-500' },
        { icon: 'Zap', color: 'text-blue-500' }
      ]
    },
    {
      id: 'user_002',
      username: 'ReactNinja',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      xp: 26890,
      level: 24,
      winRate: 85,
      streak: 12,
      battlesWon: 245,
      avgSolveTime: 195,
      favoriteLanguage: 'React',
      positionChange: 1,
      isOnline: false,
      isFollowing: true,
      recentActivity: 'Won Frontend Battle Royale',
      specialization: 'Frontend Architecture',
      badges: [
        { icon: 'Code', color: 'text-cyan-500' }
      ]
    },
    {
      id: 'user_003',
      username: 'DataStructureGuru',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      xp: 25120,
      level: 23,
      winRate: 82,
      streak: 9,
      battlesWon: 198,
      avgSolveTime: 210,
      favoriteLanguage: 'Java',
      positionChange: -1,
      isOnline: true,
      isFollowing: false,
      recentActivity: 'Completed Binary Tree Challenge',
      specialization: 'Data Structures & Algorithms',
      badges: [
        { icon: 'Brain', color: 'text-purple-500' }
      ]
    },
    {
      id: 'user_004',
      username: 'FullStackHero',
      avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',
      xp: 22340,
      level: 21,
      winRate: 78,
      streak: 6,
      battlesWon: 176,
      avgSolveTime: 225,
      favoriteLanguage: 'TypeScript',
      positionChange: 2,
      isOnline: true,
      isFollowing: true,
      recentActivity: 'Won Full Stack Challenge',
      specialization: 'Full Stack Development',
      badges: [
        { icon: 'Layers', color: 'text-green-500' }
      ]
    },
    {
      id: 'user_005',
      username: 'SystemDesignPro',
      avatar: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face',
      xp: 20890,
      level: 20,
      winRate: 76,
      streak: 4,
      battlesWon: 154,
      avgSolveTime: 280,
      favoriteLanguage: 'Go',
      positionChange: -2,
      isOnline: false,
      isFollowing: false,
      recentActivity: 'Designed Scalable Architecture',
      specialization: 'System Design',
      badges: [
        { icon: 'Network', color: 'text-orange-500' }
      ]
    },
    {
      id: 'user_006',
      username: 'CSSArtist',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      xp: 18750,
      level: 19,
      winRate: 74,
      streak: 7,
      battlesWon: 132,
      avgSolveTime: 190,
      favoriteLanguage: 'CSS',
      positionChange: 1,
      isOnline: true,
      isFollowing: false,
      recentActivity: 'Created Stunning Animation',
      specialization: 'UI/UX Design',
      badges: [
        { icon: 'Palette', color: 'text-pink-500' }
      ]
    },
    {
      id: 'user_007',
      username: 'DatabaseWizard',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      xp: 17200,
      level: 18,
      winRate: 71,
      streak: 3,
      battlesWon: 118,
      avgSolveTime: 260,
      favoriteLanguage: 'SQL',
      positionChange: 0,
      isOnline: false,
      isFollowing: true,
      recentActivity: 'Optimized Complex Query',
      specialization: 'Database Management',
      badges: [
        { icon: 'Database', color: 'text-indigo-500' }
      ]
    },
    // Current user position
    {
      ...currentUser,
      positionChange: 3
    },
    {
      id: 'user_009',
      username: 'MobileDevExpert',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      xp: 14890,
      level: 17,
      winRate: 68,
      streak: 2,
      battlesWon: 95,
      avgSolveTime: 275,
      favoriteLanguage: 'Swift',
      positionChange: -1,
      isOnline: true,
      isFollowing: false,
      recentActivity: 'Built Native iOS App',
      specialization: 'Mobile Development',
      badges: [
        { icon: 'Smartphone', color: 'text-blue-500' }
      ]
    },
    {
      id: 'user_010',
      username: 'CloudArchitect',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      xp: 13450,
      level: 16,
      winRate: 65,
      streak: 1,
      battlesWon: 87,
      avgSolveTime: 290,
      favoriteLanguage: 'Terraform',
      positionChange: 2,
      isOnline: false,
      isFollowing: false,
      recentActivity: 'Deployed Microservices',
      specialization: 'Cloud Infrastructure',
      badges: [
        { icon: 'Cloud', color: 'text-sky-500' }
      ]
    }
  ];

  // Mock achievements data
  const recentAchievements = [
    {
      id: 1,
      title: 'Speed Demon',
      description: 'Solved 10 problems in under 2 minutes',
      icon: 'Zap',
      iconColor: 'text-yellow-500',
      bgColor: 'bg-yellow-500/20',
      earnedAt: '2 hours ago'
    },
    {
      id: 2,
      title: 'Streak Master',
      description: 'Maintained 7-day win streak',
      icon: 'Flame',
      iconColor: 'text-orange-500',
      bgColor: 'bg-orange-500/20',
      earnedAt: '1 day ago'
    },
    {
      id: 3,
      title: 'Algorithm Expert',
      description: 'Won 5 algorithm battles in a row',
      icon: 'Brain',
      iconColor: 'text-purple-500',
      bgColor: 'bg-purple-500/20',
      earnedAt: '3 days ago'
    }
  ];

  // Filter and search logic
  const filteredUsers = useMemo(() => {
    let filtered = [...mockUsers];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Category filter (mock implementation)
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(user =>
        user.specialization.toLowerCase().includes(categoryFilter.toLowerCase())
      );
    }

    // Difficulty filter (mock implementation based on level)
    if (difficultyFilter !== 'all') {
      const levelRanges = {
        easy: [1, 10],
        medium: [11, 20],
        hard: [21, 30],
        expert: [31, 50]
      };
      const [min, max] = levelRanges[difficultyFilter] || [1, 50];
      filtered = filtered.filter(user => user.level >= min && user.level <= max);
    }

    return filtered;
  }, [searchQuery, categoryFilter, difficultyFilter]);

  // Find current user position
  const currentUserRank = mockUsers.findIndex(user => user.id === currentUser.id) + 1;

  // Simulate loading
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [activeTab, categoryFilter, difficultyFilter]);

  const handleClearSearch = () => {
    setSearchQuery('');
  };

  const handleLoadMore = () => {
    // Simulate loading more data
    setCurrentPage(prev => prev + 1);
    // In real app, this would trigger API call
  };

  return (
    <>
      <Helmet>
        <title>Leaderboard - PromptClash</title>
        <meta name="description" content="View competitive rankings and top performers in coding battles" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        <BattleQuickJoin />

        <main className="pt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {/* Page Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-foreground mb-2">Leaderboard</h1>
              <p className="text-text-secondary">
                Compete with the best developers and climb the ranks
              </p>
            </div>

            {/* Current User Position */}
            <CurrentUserPosition 
              user={currentUser} 
              rank={currentUserRank} 
              totalUsers={mockUsers.length} 
            />

            {/* Recent Achievements */}
            <AchievementShowcase achievements={recentAchievements} />

            {/* Controls */}
            <div className="space-y-4 mb-6">
              {/* Tabs */}
              <LeaderboardTabs activeTab={activeTab} onTabChange={setActiveTab} />

              {/* Search and Filters */}
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <SearchBar searchQuery={searchQuery} onSearchChange={setSearchQuery} />
                </div>
                <div className="lg:w-96">
                  <FilterOptions
                    categoryFilter={categoryFilter}
                    difficultyFilter={difficultyFilter}
                    onCategoryChange={setCategoryFilter}
                    onDifficultyChange={setDifficultyFilter}
                  />
                </div>
              </div>
            </div>

            {/* Leaderboard Content */}
            {isLoading ? (
              <LoadingState />
            ) : filteredUsers.length === 0 ? (
              <EmptyState searchQuery={searchQuery} onClearSearch={handleClearSearch} />
            ) : (
              <div className="space-y-4">
                {filteredUsers.map((user, index) => (
                  <UserRankCard
                    key={user.id}
                    user={user}
                    rank={index + 1}
                    isCurrentUser={user.id === currentUser.id}
                    showExtendedStats={true}
                  />
                ))}

                {/* Load More (Pagination) */}
                {hasMore && filteredUsers.length >= 10 && (
                  <div className="text-center pt-8">
                    <button
                      onClick={handleLoadMore}
                      className="px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors duration-150"
                    >
                      Load More Users
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>
    </>
  );
};

export default Leaderboard;