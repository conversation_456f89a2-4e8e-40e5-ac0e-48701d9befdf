import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const BattleCard = ({ battle, onJoin, onSpectate, onViewDetails }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'waiting':
        return 'text-success border-success/20 bg-success/5';
      case 'active':
        return 'text-warning border-warning/20 bg-warning/5';
      case 'completed':
        return 'text-text-secondary border-border bg-muted/20';
      default:
        return 'text-text-secondary border-border bg-muted/20';
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-success bg-success/10';
      case 'medium':
        return 'text-warning bg-warning/10';
      case 'hard':
        return 'text-error bg-error/10';
      default:
        return 'text-text-secondary bg-muted';
    }
  };

  const formatTimeRemaining = (seconds) => {
    if (seconds <= 0) return 'Starting soon';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div 
      className={`bg-card border rounded-xl p-6 hover:shadow-lg transition-all duration-200 cursor-pointer ${getStatusColor(battle.status)}`}
      onClick={() => onViewDetails(battle)}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="font-semibold text-foreground mb-1 line-clamp-1">
            {battle.title}
          </h3>
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-md text-xs font-medium ${getDifficultyColor(battle.difficulty)}`}>
              {battle.difficulty}
            </span>
            <span className="text-xs text-text-secondary">
              {battle.category}
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-1 text-xs text-text-secondary">
          <Icon name="Clock" size={14} />
          <span>{formatTimeRemaining(battle.timeRemaining)}</span>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          <Icon name="Users" size={16} className="text-text-secondary" />
          <span className="text-sm">
            <span className="font-medium text-foreground">{battle.participants}</span>
            <span className="text-text-secondary">/{battle.maxParticipants}</span>
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <Icon name="Eye" size={16} className="text-text-secondary" />
          <span className="text-sm font-medium text-foreground">{battle.spectators}</span>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-4">
        <div className="w-full bg-muted rounded-full h-2">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${Math.min((battle.participants / battle.maxParticipants) * 100, 100)}%` }}
          />
        </div>
      </div>

      {/* Prize */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-1 text-warning">
          <Icon name="Award" size={16} />
          <span className="text-sm font-medium">{battle.prize}</span>
        </div>
        <div className="text-xs text-text-secondary">
          {battle.language}
        </div>
      </div>

      {/* Actions */}
      <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
        {battle.status === 'waiting' && battle.participants < battle.maxParticipants && (
          <Button
            variant="default"
            size="sm"
            onClick={() => onJoin(battle)}
            className="flex-1"
          >
            <Icon name="Zap" size={16} />
            Join Battle
          </Button>
        )}
        {battle.status === 'active' && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onSpectate(battle)}
            className="flex-1"
          >
            <Icon name="Eye" size={16} />
            Spectate
          </Button>
        )}
        {battle.status === 'completed' && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onViewDetails(battle)}
            className="flex-1"
          >
            <Icon name="BarChart3" size={16} />
            View Results
          </Button>
        )}
      </div>
    </div>
  );
};

export default BattleCard;