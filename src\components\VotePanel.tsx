import { useState } from 'react'
import { useAuth } from '../context/AuthContext'
import { useBattle } from '../context/BattleContext'

interface VotePanelProps {
  battleId: string
}

const VotePanel = ({ battleId }: VotePanelProps) => {
  const { user } = useAuth()
  const { currentBattle, vote } = useBattle()
  const [hasVoted, setHasVoted] = useState(false)

  if (!currentBattle || !user) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-3">Voting</h3>
        <p className="text-gray-400 text-sm">Loading...</p>
      </div>
    )
  }

  const handleVote = (playerId: string) => {
    if (hasVoted) return
    
    vote(battleId, playerId)
    setHasVoted(true)
  }

  const player1Votes = currentBattle.player1_votes
  const player2Votes = currentBattle.player2_votes
  const totalVotes = player1Votes + player2Votes

  return (
    <div className="bg-gray-800 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-white mb-4">Vote for the Winner</h3>
      
      {hasVoted ? (
        <div className="text-center">
          <div className="text-green-400 text-lg mb-2">✅ Vote submitted!</div>
          <p className="text-gray-400 text-sm">Thanks for voting</p>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Player 1 */}
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-white">Player 1</h4>
              <span className="text-sm text-gray-400">{player1Votes} votes</span>
            </div>
            <div className="w-full bg-gray-600 rounded-full h-2 mb-3">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${totalVotes > 0 ? (player1Votes / totalVotes) * 100 : 0}%` }}
              />
            </div>
            <button
              onClick={() => handleVote(currentBattle.player1_id)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded text-sm font-medium transition-colors"
            >
              Vote for Player 1
            </button>
          </div>

          {/* Player 2 */}
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-white">Player 2</h4>
              <span className="text-sm text-gray-400">{player2Votes} votes</span>
            </div>
            <div className="w-full bg-gray-600 rounded-full h-2 mb-3">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${totalVotes > 0 ? (player2Votes / totalVotes) * 100 : 0}%` }}
              />
            </div>
            <button
              onClick={() => handleVote(currentBattle.player2_id || '')}
              className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded text-sm font-medium transition-colors"
            >
              Vote for Player 2
            </button>
          </div>
        </div>
      )}

      {/* Vote Summary */}
      <div className="mt-6 pt-4 border-t border-gray-700">
        <div className="text-center">
          <div className="text-2xl font-bold text-white mb-1">{totalVotes}</div>
          <div className="text-sm text-gray-400">Total Votes</div>
        </div>
      </div>
    </div>
  )
}

export default VotePanel 