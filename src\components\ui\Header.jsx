import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import AppIcon from '../AppIcon';
import Button from './Button';

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, userProfile, signOut } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleSignOut = async () => {
    await signOut();
    navigate('/login-register');
  };

  const isActive = (path) => location.pathname === path;

  return (
    <header className="fixed top-0 left-0 right-0 bg-card/95 backdrop-blur-md border-b border-border z-[100]">
      <div className="px-6 h-16 flex items-center justify-between">
        {/* Logo */}
        <div 
          onClick={() => navigate('/battle-lobby')}
          className="flex items-center space-x-3 cursor-pointer group"
        >
          <div className="relative">
            <AppIcon className="w-8 h-8" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-accent rounded-full group-hover:scale-110 transition-transform duration-200"></div>
          </div>
          <div>
            <h1 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors duration-200">
              PromptClash
            </h1>
            <p className="text-xs text-text-secondary -mt-1">Battle Platform</p>
          </div>
        </div>

        {/* Navigation */}
        {user && (
          <nav className="hidden md:flex items-center space-x-6">
            <button
              onClick={() => navigate('/battle-lobby')}
              className={`text-sm font-medium transition-colors duration-150 ${
                isActive('/battle-lobby') 
                  ? 'text-primary' :'text-text-secondary hover:text-foreground'
              }`}
            >
              Battle Lobby
            </button>
            <button
              onClick={() => navigate('/leaderboard')}
              className={`text-sm font-medium transition-colors duration-150 ${
                isActive('/leaderboard') 
                  ? 'text-primary' :'text-text-secondary hover:text-foreground'
              }`}
            >
              Leaderboard
            </button>
            <button
              onClick={() => navigate('/user-profile')}
              className={`text-sm font-medium transition-colors duration-150 ${
                isActive('/user-profile') 
                  ? 'text-primary' :'text-text-secondary hover:text-foreground'
              }`}
            >
              Profile
            </button>
          </nav>
        )}

        {/* User Menu */}
        <div className="flex items-center space-x-4">
          {user && userProfile ? (
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 hover:bg-muted rounded-lg px-3 py-2 transition-colors duration-150"
              >
                <div className="text-right hidden sm:block">
                  <p className="text-sm font-medium text-foreground">{userProfile.full_name}</p>
                  <p className="text-xs text-text-secondary">Level {Math.floor(userProfile.xp / 100) + 1}</p>
                </div>
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-primary-foreground">
                    {userProfile.full_name?.charAt(0) || 'U'}
                  </span>
                </div>
              </button>

              {/* User Dropdown */}
              {showUserMenu && (
                <>
                  <div 
                    className="fixed inset-0 z-[150]"
                    onClick={() => setShowUserMenu(false)}
                  />
                  <div className="absolute right-0 top-full mt-2 w-64 bg-card border border-border rounded-lg shadow-elevation z-[200]">
                    <div className="p-4 border-b border-border">
                      <p className="font-medium text-foreground">{userProfile.full_name}</p>
                      <p className="text-sm text-text-secondary">{userProfile.email}</p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-text-secondary">
                        <span>XP: {userProfile.xp}</span>
                        <span>Wins: {userProfile.total_wins}</span>
                        <span>Streak: {userProfile.win_streak}</span>
                      </div>
                    </div>
                    <div className="p-2">
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          navigate('/user-profile');
                        }}
                        className="w-full text-left px-3 py-2 text-sm text-foreground hover:bg-muted rounded-md transition-colors duration-150"
                      >
                        View Profile
                      </button>
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          handleSignOut();
                        }}
                        className="w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-md transition-colors duration-150"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          ) : (
            <Button onClick={() => navigate('/login-register')}>
              Sign In
            </Button>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;