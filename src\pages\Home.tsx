import { Link } from 'react-router-dom'
import { useAuth } from '../context/AuthContext'

const Home = () => {
  const { user } = useAuth()

  const featuredBattles = [
    {
      id: '1',
      title: 'Build a Todo App',
      participants: 12,
      timeLeft: '2h 15m',
      difficulty: 'Easy'
    },
    {
      id: '2',
      title: 'Create a Weather Dashboard',
      participants: 8,
      timeLeft: '1h 30m',
      difficulty: 'Medium'
    },
    {
      id: '3',
      title: 'Design a Chat Interface',
      participants: 15,
      timeLeft: '45m',
      difficulty: 'Hard'
    }
  ]

  const recentPrompts = [
    {
      id: '1',
      title: 'Build a Calculator',
      author: 'CodeMaster',
      votes: 24,
      category: 'JavaScript'
    },
    {
      id: '2',
      title: 'Create a Portfolio Site',
      author: 'WebDevPro',
      votes: 18,
      category: 'React'
    },
    {
      id: '3',
      title: 'Design a Game UI',
      author: 'GameDev',
      votes: 31,
      category: 'CSS'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center py-12">
        <h1 className="text-5xl font-bold mb-4">
          🚀 Welcome to PromptClash
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
          Battle other coders in real-time prompt challenges. Code, compete, and win!
        </p>
        <div className="flex justify-center space-x-4">
          {user ? (
            <Link
              to="/battle"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold text-lg"
            >
              Join Battle
            </Link>
          ) : (
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold text-lg">
              Sign In to Battle
            </button>
          )}
          <Link
            to="/create"
            className="bg-gray-700 hover:bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold text-lg"
          >
            Create Prompt
          </Link>
        </div>
      </div>

      {/* Featured Battles */}
      <div>
        <h2 className="text-2xl font-bold mb-6">🔥 Live Battles</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredBattles.map((battle) => (
            <div key={battle.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold mb-2">{battle.title}</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <p>👥 {battle.participants} participants</p>
                <p>⏰ {battle.timeLeft} left</p>
                <p>📊 Difficulty: {battle.difficulty}</p>
              </div>
              <Link
                to={`/battle/${battle.id}`}
                className="mt-4 inline-block bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium"
              >
                Join Battle
              </Link>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Prompts */}
      <div>
        <h2 className="text-2xl font-bold mb-6">📝 Recent Prompts</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recentPrompts.map((prompt) => (
            <div key={prompt.id} className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h3 className="text-lg font-semibold mb-2">{prompt.title}</h3>
              <div className="space-y-2 text-sm text-gray-300">
                <p>👤 by {prompt.author}</p>
                <p>👍 {prompt.votes} votes</p>
                <p>🏷️ {prompt.category}</p>
              </div>
              <button className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium">
                Vote
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-gray-800 rounded-lg p-8 border border-gray-700">
        <h2 className="text-2xl font-bold mb-6 text-center">📊 Platform Stats</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
          <div>
            <div className="text-3xl font-bold text-blue-400">1,247</div>
            <div className="text-gray-300">Total Battles</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-green-400">5,892</div>
            <div className="text-gray-300">Active Coders</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-purple-400">892</div>
            <div className="text-gray-300">Prompts Created</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-yellow-400">24.5K</div>
            <div className="text-gray-300">Lines of Code</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Home 