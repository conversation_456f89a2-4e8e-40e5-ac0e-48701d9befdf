import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const BattleQuickJoin = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isVisible, setIsVisible] = useState(true);
  const [nextBattle, setNextBattle] = useState({
    id: 'battle_001',
    title: 'Algorithm Sprint',
    category: 'Data Structures',
    difficulty: 'Medium',
    participants: 12,
    maxParticipants: 20,
    startTime: '2 min',
    prize: '500 XP'
  });
  const [isJoining, setIsJoining] = useState(false);

  // Hide widget on battle-related pages
  useEffect(() => {
    const battlePages = ['/battle-room', '/battle-results'];
    setIsVisible(!battlePages.includes(location.pathname));
  }, [location.pathname]);

  const handleQuickJoin = async () => {
    setIsJoining(true);
    
    // Simulate API call
    setTimeout(() => {
      navigate('/battle-room');
      setIsJoining(false);
    }, 1000);
  };

  const handleViewDetails = () => {
    navigate('/battle-lobby');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-[1000]">
      <div className="bg-card border border-border rounded-xl shadow-elevation p-4 w-80 battle-glow">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-success">Next Battle</span>
          </div>
          <span className="text-xs text-text-secondary">Starts in {nextBattle.startTime}</span>
        </div>

        {/* Battle Info */}
        <div className="mb-4">
          <h3 className="font-semibold text-foreground mb-1">{nextBattle.title}</h3>
          <div className="flex items-center space-x-4 text-sm text-text-secondary">
            <span className="flex items-center space-x-1">
              <Icon name="Tag" size={14} />
              <span>{nextBattle.category}</span>
            </span>
            <span className="flex items-center space-x-1">
              <Icon name="Zap" size={14} />
              <span>{nextBattle.difficulty}</span>
            </span>
          </div>
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mb-4 text-sm">
          <div className="flex items-center space-x-1 text-text-secondary">
            <Icon name="Users" size={14} />
            <span>{nextBattle.participants}/{nextBattle.maxParticipants} players</span>
          </div>
          <div className="flex items-center space-x-1 text-warning">
            <Icon name="Award" size={14} />
            <span>{nextBattle.prize}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="w-full bg-muted rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${(nextBattle.participants / nextBattle.maxParticipants) * 100}%` }}
            ></div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleViewDetails}
            className="flex-1"
          >
            <Icon name="Eye" size={16} />
            Details
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleQuickJoin}
            loading={isJoining}
            className="flex-1"
          >
            <Icon name="Zap" size={16} />
            Quick Join
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BattleQuickJoin;