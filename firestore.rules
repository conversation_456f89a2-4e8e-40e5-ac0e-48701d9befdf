rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Prompts collection - anyone can read, authenticated users can write
    match /prompts/{promptId} {
      allow read: if true;
      allow write: if request.auth != null;
    }
    
    // Battles collection - authenticated users can read/write
    match /battles/{battleId} {
      allow read, write: if request.auth != null;
    }
    
    // Votes collection - authenticated users can read/write
    match /votes/{voteId} {
      allow read, write: if request.auth != null;
    }
  }
} 