// Firebase Setup Automation Script
// This script helps automate some Firebase setup steps

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 PromptClash Firebase Setup Automation');
console.log('=====================================\n');

// Check if Firebase CLI is installed
function checkFirebaseCLI() {
  try {
    execSync('firebase --version', { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

// Install Firebase CLI if not present
function installFirebaseCLI() {
  console.log('📦 Installing Firebase CLI...');
  try {
    execSync('npm install -g firebase-tools', { stdio: 'inherit' });
    console.log('✅ Firebase CLI installed successfully!\n');
    return true;
  } catch (error) {
    console.log('❌ Failed to install Firebase CLI. Please install manually:');
    console.log('   npm install -g firebase-tools\n');
    return false;
  }
}

// Initialize Firebase project
function initializeFirebase() {
  console.log('🔧 Initializing Firebase project...');
  
  // Create firebase.json if it doesn't exist
  if (!fs.existsSync('firebase.json')) {
    const firebaseConfig = {
      "firestore": {
        "rules": "firestore.rules",
        "indexes": "firestore.indexes.json"
      },
      "database": {
        "rules": "database.rules.json"
      },
      "hosting": {
        "public": "dist",
        "ignore": [
          "firebase.json",
          "**/.*",
          "**/node_modules/**"
        ],
        "rewrites": [
          {
            "source": "**",
            "destination": "/index.html"
          }
        ]
      }
    };
    
    fs.writeFileSync('firebase.json', JSON.stringify(firebaseConfig, null, 2));
    console.log('✅ Created firebase.json');
  }
  
  // Create firestore.indexes.json if it doesn't exist
  if (!fs.existsSync('firestore.indexes.json')) {
    const indexesConfig = {
      "indexes": [
        {
          "collectionGroup": "battles",
          "queryScope": "COLLECTION",
          "fields": [
            { "fieldPath": "status", "order": "ASCENDING" },
            { "fieldPath": "created_at", "order": "DESCENDING" }
          ]
        },
        {
          "collectionGroup": "prompts",
          "queryScope": "COLLECTION",
          "fields": [
            { "fieldPath": "difficulty", "order": "ASCENDING" },
            { "fieldPath": "created_at", "order": "DESCENDING" }
          ]
        }
      ],
      "fieldOverrides": []
    };
    
    fs.writeFileSync('firestore.indexes.json', JSON.stringify(indexesConfig, null, 2));
    console.log('✅ Created firestore.indexes.json');
  }
}

// Deploy security rules
function deployRules() {
  console.log('🔒 Deploying security rules...');
  try {
    // Deploy Firestore rules
    execSync('firebase deploy --only firestore:rules', { stdio: 'inherit' });
    console.log('✅ Firestore rules deployed');
    
    // Deploy Realtime Database rules
    execSync('firebase deploy --only database', { stdio: 'inherit' });
    console.log('✅ Realtime Database rules deployed');
    
    return true;
  } catch (error) {
    console.log('❌ Failed to deploy rules. You may need to:');
    console.log('   1. Run: firebase login');
    console.log('   2. Run: firebase use vibecodefeed');
    console.log('   3. Try deploying rules manually in Firebase Console\n');
    return false;
  }
}

// Add sample data
function addSampleData() {
  console.log('📝 Adding sample prompts...');
  
  const samplePrompts = [
    {
      text: "Create a responsive navigation bar with a hamburger menu for mobile devices",
      category: "frontend",
      constraints: {
        maxChars: 500,
        language: "HTML/CSS/JS",
        bannedKeywords: ["bootstrap", "jquery"]
      },
      difficulty: "easy",
      created_at: new Date()
    },
    {
      text: "Build a simple calculator that can perform basic arithmetic operations",
      category: "frontend",
      constraints: {
        maxChars: 300,
        language: "JavaScript",
        bannedKeywords: ["eval", "Function"]
      },
      difficulty: "easy",
      created_at: new Date()
    },
    {
      text: "Create a todo list with add, delete, and mark as complete functionality",
      category: "frontend",
      constraints: {
        maxChars: 400,
        language: "React",
        bannedKeywords: ["localStorage", "database"]
      },
      difficulty: "medium",
      created_at: new Date()
    }
  ];
  
  console.log('Sample prompts ready to add:');
  console.log(JSON.stringify(samplePrompts, null, 2));
  console.log('\n📋 To add these prompts:');
  console.log('   1. Go to Firebase Console > Firestore Database');
  console.log('   2. Create collection "prompts"');
  console.log('   3. Add each prompt as a document');
}

// Main setup function
function main() {
  console.log('Starting automated Firebase setup...\n');
  
  // Check and install Firebase CLI
  if (!checkFirebaseCLI()) {
    if (!installFirebaseCLI()) {
      console.log('❌ Setup cannot continue without Firebase CLI');
      return;
    }
  }
  
  // Initialize Firebase configuration
  initializeFirebase();
  
  // Try to deploy rules
  deployRules();
  
  // Show sample data
  addSampleData();
  
  console.log('\n🎉 Automated setup complete!');
  console.log('\n📋 Manual steps remaining:');
  console.log('   1. Enable Google Authentication in Firebase Console');
  console.log('   2. Create Firestore Database (if not exists)');
  console.log('   3. Create Realtime Database (if not exists)');
  console.log('   4. Add sample prompts to Firestore');
  console.log('\n🔗 Quick links:');
  console.log('   - Auth: https://console.firebase.google.com/project/vibecodefeed/authentication/providers');
  console.log('   - Firestore: https://console.firebase.google.com/project/vibecodefeed/firestore');
  console.log('   - Realtime DB: https://console.firebase.google.com/project/vibecodefeed/realtime-database');
}

// Run the setup
main(); 