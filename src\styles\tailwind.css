@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core Colors */
    --color-background: #0F172A; /* slate-900 */
    --color-foreground: #F8FAFC; /* slate-50 */
    --color-border: rgba(148, 163, 184, 0.2); /* slate-400 with opacity */
    --color-input: #1E293B; /* slate-800 */
    --color-ring: #00D9FF; /* cyan-400 */
    
    /* Card Colors */
    --color-card: #1E293B; /* slate-800 */
    --color-card-foreground: #F8FAFC; /* slate-50 */
    
    /* Popover Colors */
    --color-popover: #1E293B; /* slate-800 */
    --color-popover-foreground: #F8FAFC; /* slate-50 */
    
    /* Muted Colors */
    --color-muted: #334155; /* slate-700 */
    --color-muted-foreground: #94A3B8; /* slate-400 */
    
    /* Primary Colors */
    --color-primary: #00D9FF; /* cyan-400 */
    --color-primary-foreground: #0F172A; /* slate-900 */
    
    /* Secondary Colors */
    --color-secondary: #7C3AED; /* violet-600 */
    --color-secondary-foreground: #F8FAFC; /* slate-50 */
    
    /* Accent Colors */
    --color-accent: #10B981; /* emerald-500 */
    --color-accent-foreground: #F8FAFC; /* slate-50 */
    
    /* Success Colors */
    --color-success: #22C55E; /* green-500 */
    --color-success-foreground: #F8FAFC; /* slate-50 */
    
    /* Warning Colors */
    --color-warning: #F59E0B; /* amber-500 */
    --color-warning-foreground: #0F172A; /* slate-900 */
    
    /* Error/Destructive Colors */
    --color-error: #EF4444; /* red-500 */
    --color-error-foreground: #F8FAFC; /* slate-50 */
    --color-destructive: #EF4444; /* red-500 */
    --color-destructive-foreground: #F8FAFC; /* slate-50 */
    
    /* Surface Color */
    --color-surface: #1E293B; /* slate-800 */
    
    /* Text Colors */
    --color-text-primary: #F8FAFC; /* slate-50 */
    --color-text-secondary: #94A3B8; /* slate-400 */
  }
  
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  .text-shadow-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  .battle-glow {
    box-shadow: 0 0 20px rgba(0, 217, 255, 0.3);
  }
  
  .pulse-border {
    animation: pulse-border 2s ease-in-out infinite;
  }
  
  @keyframes pulse-border {
    0%, 100% {
      border-color: var(--color-primary);
      opacity: 1;
    }
    50% {
      border-color: var(--color-primary);
      opacity: 0.5;
    }
  }
}