import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const SolutionCard = ({ solution, onVote, userVote, isExpanded, onToggleExpand }) => {
  const [showPreview, setShowPreview] = useState(false);

  const handleVote = (type) => {
    if (userVote === type) {
      onVote(solution.id, null); // Remove vote
    } else {
      onVote(solution.id, type);
    }
  };

  const getLanguageIcon = (language) => {
    const icons = {
      javascript: 'FileText',
      python: 'Code',
      java: 'Coffee',
      cpp: 'Zap',
      typescript: 'FileCode'
    };
    return icons[language.toLowerCase()] || 'Code';
  };

  const formatCode = (code) => {
    return code.split('\n').map((line, index) => (
      <div key={index} className="flex">
        <span className="text-text-secondary text-sm mr-4 select-none w-8 text-right">
          {index + 1}
        </span>
        <span className="text-foreground">{line}</span>
      </div>
    ));
  };

  return (
    <motion.div
      className="bg-card border border-border rounded-xl overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <Image 
                src={solution.user.avatar} 
                alt={solution.user.username}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h3 className="font-semibold text-foreground">{solution.user.username}</h3>
              <div className="flex items-center space-x-2 text-sm text-text-secondary">
                <span>Level {solution.user.level}</span>
                <span>•</span>
                <span>{solution.submittedAt}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-sm text-text-secondary">
              <Icon name={getLanguageIcon(solution.language)} size={16} />
              <span>{solution.language}</span>
            </div>
            <div className="text-sm text-primary font-mono">
              {solution.executionTime}ms
            </div>
          </div>
        </div>
      </div>

      {/* Code Preview */}
      <div className="p-4">
        <div className="bg-muted rounded-lg overflow-hidden">
          <div className="flex items-center justify-between px-4 py-2 bg-background border-b border-border">
            <span className="text-sm font-medium text-text-secondary">Solution Code</span>
            <div className="flex items-center space-x-2">
              {solution.hasPreview && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                  iconName={showPreview ? 'EyeOff' : 'Eye'}
                  iconPosition="left"
                >
                  {showPreview ? 'Hide' : 'Preview'}
                </Button>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onToggleExpand(solution.id)}
                iconName={isExpanded ? 'ChevronUp' : 'ChevronDown'}
              >
                {isExpanded ? 'Collapse' : 'Expand'}
              </Button>
            </div>
          </div>
          
          <div className="p-4 font-mono text-sm overflow-x-auto">
            <div className={`transition-all duration-300 ${isExpanded ? 'max-h-none' : 'max-h-32 overflow-hidden'}`}>
              {formatCode(solution.code)}
            </div>
            {!isExpanded && solution.code.split('\n').length > 8 && (
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent"></div>
            )}
          </div>
        </div>

        {/* Live Preview */}
        {showPreview && solution.hasPreview && (
          <div className="mt-4 bg-background border border-border rounded-lg overflow-hidden">
            <div className="px-4 py-2 bg-muted border-b border-border">
              <span className="text-sm font-medium text-text-secondary">Live Preview</span>
            </div>
            <div className="p-4">
              <div className="bg-white rounded border min-h-32 flex items-center justify-center">
                <span className="text-gray-500">Preview would render here</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Voting & Stats */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Button
                variant={userVote === 'up' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => handleVote('up')}
                iconName="ThumbsUp"
                iconPosition="left"
                className={userVote === 'up' ? 'text-success' : ''}
              >
                {solution.upvotes}
              </Button>
              <Button
                variant={userVote === 'down' ? 'destructive' : 'ghost'}
                size="sm"
                onClick={() => handleVote('down')}
                iconName="ThumbsDown"
                iconPosition="left"
              >
                {solution.downvotes}
              </Button>
            </div>
            
            <div className="text-sm text-text-secondary">
              {solution.totalVotes} votes
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              iconName="MessageCircle"
              iconPosition="left"
            >
              {solution.comments}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              iconName="Share"
            >
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default SolutionCard;