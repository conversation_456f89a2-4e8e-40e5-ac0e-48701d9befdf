@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-900 text-white;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-gray-800 rounded-lg border border-gray-700 p-6 shadow-lg;
  }
  
  .input-field {
    @apply bg-gray-700 border border-gray-600 text-white placeholder-gray-400 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .battle-container {
    @apply grid grid-cols-1 lg:grid-cols-2 gap-6 h-screen p-6;
  }
  
  .editor-container {
    @apply bg-gray-800 rounded-lg border border-gray-700 overflow-hidden;
  }
  
  .timer-display {
    @apply text-4xl font-bold text-center py-4;
  }
  
  .timer-warning {
    @apply text-red-500 animate-pulse;
  }
  
  .typing-indicator {
    @apply text-sm text-gray-400 italic;
  }
  
  .vote-button {
    @apply flex-1 py-3 px-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105;
  }
  
  .vote-player1 {
    @apply bg-battle-red hover:bg-red-700 text-white;
  }
  
  .vote-player2 {
    @apply bg-battle-blue hover:bg-blue-700 text-white;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-purple-400 bg-clip-text text-transparent;
  }
  
  .glow-effect {
    @apply shadow-lg shadow-primary-500/25;
  }
  
  .animate-typing {
    animation: typing 1s infinite;
  }
  
  @keyframes typing {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }
}

/* Monaco Editor Customization */
.monaco-editor {
  @apply rounded-lg;
}

.monaco-editor .margin {
  @apply bg-gray-700;
}

.monaco-editor .monaco-editor-background {
  @apply bg-gray-800;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
} 