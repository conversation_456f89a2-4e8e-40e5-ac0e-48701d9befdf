import { supabase } from './supabaseClient';

const chatService = {
  // Send message to battle chat
  async sendMessage(battleId, content, messageType = 'message') {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('chat_messages')
        .insert({
          battle_id: battleId,
          user_id: user.user.id,
          content,
          message_type: messageType
        })
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url)
        `)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to send message' }
    }
  },

  // Get chat messages for a battle
  async getChatMessages(battleId, limit = 50) {
    try {
      const { data, error } = await supabase
        .from('chat_messages')
        .select(`
          *,
          user:user_profiles(id, username, full_name, avatar_url)
        `)
        .eq('battle_id', battleId)
        .order('created_at', { ascending: true })
        .limit(limit)

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load messages' }
    }
  },

  // Subscribe to new chat messages
  subscribeToChatMessages(battleId, onNewMessage) {
    const channel = supabase
      .channel(`chat-${battleId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `battle_id=eq.${battleId}`
        },
        async (payload) => {
          // Fetch the complete message with user data
          const { data, error } = await supabase
            .from('chat_messages')
            .select(`
              *,
              user:user_profiles(id, username, full_name, avatar_url)
            `)
            .eq('id', payload.new.id)
            .single()

          if (!error && data) {
            onNewMessage(data)
          }
        }
      )
      .subscribe()

    return channel
  }
}

export default chatService