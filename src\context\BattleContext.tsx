import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { useParams } from 'react-router-dom'
import { 
  listenToBattle, 
  updateBattle, 
  syncCode, 
  listenToCode, 
  listenToTyping, 
  setTyping,
  cleanupListeners,
  submitVote,
  getBattleVotes
} from '../services/firebaseClient'
import { Battle, BattleState, Vote } from '../types'
import { useAuth } from './AuthContext'

interface BattleContextType extends BattleState {
  joinBattle: (battleId: string, userId: string) => void
  leaveBattle: (battleId: string, userId: string) => void
  updateCode: (battleId: string, userId: string, code: string) => void
  startBattle: (battleId: string) => void
  endBattle: (battleId: string) => void
  vote: (battleId: string, winnerId: string) => void
}

const BattleContext = createContext<BattleContextType | undefined>(undefined)

type BattleAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_BATTLE'; payload: Battle | null }
  | { type: 'UPDATE_CODE'; payload: { battleId: string; userId: string; code: string } }
  | { type: 'ADD_VOTE'; payload: Vote }

const battleReducer = (state: BattleState, action: BattleAction): BattleState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    case 'SET_ERROR':
      return { ...state, error: action.payload }
    case 'SET_BATTLE':
      return { ...state, currentBattle: action.payload }
    case 'UPDATE_CODE':
      if (!state.currentBattle) return state
      return {
        ...state,
        currentBattle: {
          ...state.currentBattle,
          player1_code: state.currentBattle.player1_id === action.payload.userId 
            ? action.payload.code 
            : state.currentBattle.player1_code,
          player2_code: state.currentBattle.player2_id === action.payload.userId 
            ? action.payload.code 
            : state.currentBattle.player2_code
        }
      }
    case 'ADD_VOTE':
      if (!state.currentBattle) return state
      return {
        ...state,
        currentBattle: {
          ...state.currentBattle,
          player1_votes: action.payload.winner_id === state.currentBattle.player1_id 
            ? state.currentBattle.player1_votes + 1 
            : state.currentBattle.player1_votes,
          player2_votes: action.payload.winner_id === state.currentBattle.player2_id 
            ? state.currentBattle.player2_votes + 1 
            : state.currentBattle.player2_votes
        }
      }
    default:
      return state
  }
}

export const BattleProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(battleReducer, {
    currentBattle: null,
    isLoading: false,
    error: null
  })
  const { user } = useAuth()

  const joinBattle = (battleId: string, userId: string) => {
    dispatch({ type: 'SET_LOADING', payload: true })
    
    // Listen to battle updates
    const unsubscribe = listenToBattle(battleId, (battle) => {
      dispatch({ type: 'SET_BATTLE', payload: battle })
      dispatch({ type: 'SET_LOADING', payload: false })
    })

    // Listen to opponent's code
    if (user) {
      const opponentId = state.currentBattle?.player1_id === userId 
        ? state.currentBattle.player2_id 
        : state.currentBattle?.player1_id

      if (opponentId) {
        listenToCode(battleId, opponentId, (code) => {
          dispatch({ type: 'UPDATE_CODE', payload: { battleId, userId: opponentId, code } })
        })
      }
    }

    return unsubscribe
  }

  const leaveBattle = (battleId: string, userId: string) => {
    cleanupListeners(battleId)
  }

  const updateCode = (battleId: string, userId: string, code: string) => {
    dispatch({ type: 'UPDATE_CODE', payload: { battleId, userId, code } })
    syncCode(battleId, userId, code)
    
    // Set typing indicator
    setTyping(battleId, userId, true)
    setTimeout(() => setTyping(battleId, userId, false), 1000)
  }

  const startBattle = (battleId: string) => {
    updateBattle(battleId, {
      status: 'active'
    })
  }

  const endBattle = (battleId: string) => {
    if (!state.currentBattle) return

    const winnerId = state.currentBattle.player1_votes > state.currentBattle.player2_votes 
      ? state.currentBattle.player1_id 
      : state.currentBattle.player2_id

    updateBattle(battleId, {
      status: 'completed',
      ended_at: new Date()
    })
  }

  const vote = async (battleId: string, winnerId: string) => {
    if (!user) return

    try {
      await submitVote({
        battle_id: battleId,
        voter_id: user.id,
        winner_id: winnerId
      })

      dispatch({ type: 'ADD_VOTE', payload: {
        id: '',
        battle_id: battleId,
        voter_id: user.id,
        winner_id: winnerId,
        created_at: new Date()
      }})
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to submit vote' })
    }
  }

  return (
    <BattleContext.Provider value={{
      ...state,
      joinBattle,
      leaveBattle,
      updateCode,
      startBattle,
      endBattle,
      vote
    }}>
      {children}
    </BattleContext.Provider>
  )
}

export const useBattle = () => {
  const context = useContext(BattleContext)
  if (context === undefined) {
    throw new Error('useBattle must be used within a BattleProvider')
  }
  return context
} 