import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';
import { Checkbox } from '../../../components/ui/Checkbox';

const SettingsTab = ({ user, onSaveSettings }) => {
  const [settings, setSettings] = useState({
    // Profile Settings
    username: user.username,
    email: user.email,
    bio: user.bio || '',
    location: user.location || '',
    website: user.website || '',
    
    // Privacy Settings
    profileVisibility: user.profileVisibility || 'public',
    showEmail: user.showEmail || false,
    showLocation: user.showLocation || true,
    allowFriendRequests: user.allowFriendRequests || true,
    showOnlineStatus: user.showOnlineStatus || true,
    
    // Notification Settings
    emailNotifications: user.emailNotifications || true,
    battleInvites: user.battleInvites || true,
    friendRequests: user.friendRequests || true,
    achievementUnlocks: user.achievementUnlocks || true,
    weeklyDigest: user.weeklyDigest || true,
    marketingEmails: user.marketingEmails || false,
    
    // Battle Preferences
    preferredDifficulty: user.preferredDifficulty || 'medium',
    autoJoinBattles: user.autoJoinBattles || false,
    showCodeDuringBattle: user.showCodeDuringBattle || true,
    allowSpectators: user.allowSpectators || true,
    
    // Theme & Display
    theme: user.theme || 'dark',
    language: user.language || 'en',
    timezone: user.timezone || 'UTC'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const profileVisibilityOptions = [
    { value: 'public', label: 'Public - Anyone can view' },
    { value: 'friends', label: 'Friends Only' },
    { value: 'private', label: 'Private - Only me' }
  ];

  const difficultyOptions = [
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' },
    { value: 'mixed', label: 'Mixed' }
  ];

  const themeOptions = [
    { value: 'dark', label: 'Dark Theme' },
    { value: 'light', label: 'Light Theme' },
    { value: 'auto', label: 'Auto (System)' }
  ];

  const languageOptions = [
    { value: 'en', label: 'English' },
    { value: 'es', label: 'Spanish' },
    { value: 'fr', label: 'French' },
    { value: 'de', label: 'German' },
    { value: 'ja', label: 'Japanese' }
  ];

  const timezoneOptions = [
    { value: 'UTC', label: 'UTC' },
    { value: 'America/New_York', label: 'Eastern Time' },
    { value: 'America/Chicago', label: 'Central Time' },
    { value: 'America/Denver', label: 'Mountain Time' },
    { value: 'America/Los_Angeles', label: 'Pacific Time' },
    { value: 'Europe/London', label: 'London' },
    { value: 'Europe/Paris', label: 'Paris' },
    { value: 'Asia/Tokyo', label: 'Tokyo' }
  ];

  const handleInputChange = (field, value) => {
    setSettings(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!settings.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (settings.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }
    
    if (!settings.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(settings.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (settings.website && !settings.website.startsWith('http')) {
      newErrors.website = 'Website must start with http:// or https://';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    try {
      await onSaveSettings(settings);
      // Show success message
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteAccount = () => {
    if (window.confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      console.log('Account deletion requested');
    }
  };

  return (
    <div className="space-y-6">
      {/* Profile Information */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="User" size={20} />
          <span>Profile Information</span>
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Input
            label="Username"
            type="text"
            value={settings.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            error={errors.username}
            required
          />
          <Input
            label="Email Address"
            type="email"
            value={settings.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={errors.email}
            required
          />
          <Input
            label="Location"
            type="text"
            value={settings.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            placeholder="City, Country"
          />
          <Input
            label="Website"
            type="url"
            value={settings.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            error={errors.website}
            placeholder="https://yourwebsite.com"
          />
        </div>
        <div className="mt-4">
          <Input
            label="Bio"
            type="text"
            value={settings.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            placeholder="Tell us about yourself..."
            className="w-full"
          />
        </div>
      </div>

      {/* Privacy Settings */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="Shield" size={20} />
          <span>Privacy Settings</span>
        </h3>
        <div className="space-y-4">
          <Select
            label="Profile Visibility"
            description="Control who can view your profile"
            options={profileVisibilityOptions}
            value={settings.profileVisibility}
            onChange={(value) => handleInputChange('profileVisibility', value)}
          />
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Checkbox
              label="Show email address"
              checked={settings.showEmail}
              onChange={(e) => handleInputChange('showEmail', e.target.checked)}
            />
            <Checkbox
              label="Show location"
              checked={settings.showLocation}
              onChange={(e) => handleInputChange('showLocation', e.target.checked)}
            />
            <Checkbox
              label="Allow friend requests"
              checked={settings.allowFriendRequests}
              onChange={(e) => handleInputChange('allowFriendRequests', e.target.checked)}
            />
            <Checkbox
              label="Show online status"
              checked={settings.showOnlineStatus}
              onChange={(e) => handleInputChange('showOnlineStatus', e.target.checked)}
            />
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="Bell" size={20} />
          <span>Notification Settings</span>
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Checkbox
            label="Email notifications"
            description="Receive notifications via email"
            checked={settings.emailNotifications}
            onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
          />
          <Checkbox
            label="Battle invitations"
            description="Get notified of battle invites"
            checked={settings.battleInvites}
            onChange={(e) => handleInputChange('battleInvites', e.target.checked)}
          />
          <Checkbox
            label="Friend requests"
            description="Notifications for new friend requests"
            checked={settings.friendRequests}
            onChange={(e) => handleInputChange('friendRequests', e.target.checked)}
          />
          <Checkbox
            label="Achievement unlocks"
            description="Celebrate your achievements"
            checked={settings.achievementUnlocks}
            onChange={(e) => handleInputChange('achievementUnlocks', e.target.checked)}
          />
          <Checkbox
            label="Weekly digest"
            description="Summary of your weekly activity"
            checked={settings.weeklyDigest}
            onChange={(e) => handleInputChange('weeklyDigest', e.target.checked)}
          />
          <Checkbox
            label="Marketing emails"
            description="Updates about new features"
            checked={settings.marketingEmails}
            onChange={(e) => handleInputChange('marketingEmails', e.target.checked)}
          />
        </div>
      </div>

      {/* Battle Preferences */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="Swords" size={20} />
          <span>Battle Preferences</span>
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
          <Select
            label="Preferred Difficulty"
            description="Default difficulty for quick join"
            options={difficultyOptions}
            value={settings.preferredDifficulty}
            onChange={(value) => handleInputChange('preferredDifficulty', value)}
          />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Checkbox
            label="Auto-join battles"
            description="Automatically join matching battles"
            checked={settings.autoJoinBattles}
            onChange={(e) => handleInputChange('autoJoinBattles', e.target.checked)}
          />
          <Checkbox
            label="Show code during battle"
            description="Allow others to see your code"
            checked={settings.showCodeDuringBattle}
            onChange={(e) => handleInputChange('showCodeDuringBattle', e.target.checked)}
          />
          <Checkbox
            label="Allow spectators"
            description="Let others watch your battles"
            checked={settings.allowSpectators}
            onChange={(e) => handleInputChange('allowSpectators', e.target.checked)}
          />
        </div>
      </div>

      {/* Theme & Display */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="Palette" size={20} />
          <span>Theme & Display</span>
        </h3>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <Select
            label="Theme"
            options={themeOptions}
            value={settings.theme}
            onChange={(value) => handleInputChange('theme', value)}
          />
          <Select
            label="Language"
            options={languageOptions}
            value={settings.language}
            onChange={(value) => handleInputChange('language', value)}
          />
          <Select
            label="Timezone"
            options={timezoneOptions}
            value={settings.timezone}
            onChange={(value) => handleInputChange('timezone', value)}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-border">
        <Button
          variant="destructive"
          onClick={handleDeleteAccount}
          iconName="Trash2"
          iconPosition="left"
        >
          Delete Account
        </Button>
        <div className="flex space-x-3">
          <Button variant="outline">
            Reset to Defaults
          </Button>
          <Button
            variant="default"
            onClick={handleSave}
            loading={isLoading}
            iconName="Save"
            iconPosition="left"
          >
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SettingsTab;