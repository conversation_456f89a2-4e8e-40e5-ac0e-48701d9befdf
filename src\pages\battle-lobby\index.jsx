import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/ui/Header';
import BattleQuickJoin from '../../components/ui/BattleQuickJoin';
import BattleCard from './components/BattleCard';
import FilterBar from './components/FilterBar';
import LeaderboardPreview from './components/LeaderboardPreview';
import RecentWinners from './components/RecentWinners';
import BattleDetailsModal from './components/BattleDetailsModal';
import CreateBattleFAB from './components/CreateBattleFAB';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const BattleLobby = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedBattle, setSelectedBattle] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showSidePanel, setShowSidePanel] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const battles = [
    {
      id: 'battle_001',
      title: 'React Hooks Challenge',
      category: 'frontend',
      difficulty: 'Medium',
      participants: 6,
      maxParticipants: 8,
      spectators: 23,
      timeRemaining: 180,
      prize: '500 XP',
      language: 'JavaScript',
      status: 'waiting',
      duration: 30,
      description: `Build a custom React hook that manages form state with validation. \nImplement proper error handling and optimize for performance.`
    },
    {
      id: 'battle_002',
      title: 'Algorithm Sprint',
      category: 'algorithms',
      difficulty: 'Hard',
      participants: 4,
      maxParticipants: 6,
      spectators: 45,
      timeRemaining: 420,
      prize: '750 XP',
      language: 'Python',
      status: 'waiting',
      duration: 45,
      description: `Solve complex algorithmic problems involving dynamic programming and graph traversal. \nOptimize for both time and space complexity.`
    },
    {
      id: 'battle_003',
      title: 'CSS Grid Master',
      category: 'frontend',
      difficulty: 'Easy',
      participants: 8,
      maxParticipants: 10,
      spectators: 12,
      timeRemaining: 0,
      prize: '300 XP',
      language: 'CSS',
      status: 'active',
      duration: 20
    },
    {
      id: 'battle_004',
      title: 'Database Query Pro',
      category: 'database',
      difficulty: 'Medium',
      participants: 5,
      maxParticipants: 8,
      spectators: 18,
      timeRemaining: 600,
      prize: '400 XP',
      language: 'SQL',
      status: 'waiting',
      duration: 35
    },
    {
      id: 'battle_005',
      title: 'Full Stack API',
      category: 'fullstack',
      difficulty: 'Hard',
      participants: 3,
      maxParticipants: 6,
      spectators: 31,
      timeRemaining: 900,
      prize: '800 XP',
      language: 'Node.js',
      status: 'waiting',
      duration: 60
    },
    {
      id: 'battle_006',
      title: 'Binary Tree Traversal',
      category: 'algorithms',
      difficulty: 'Medium',
      participants: 6,
      maxParticipants: 6,
      spectators: 8,
      timeRemaining: 0,
      prize: '450 XP',
      language: 'Java',
      status: 'completed',
      duration: 25
    }
  ];

  const filteredBattles = battles.filter(battle => {
    const categoryMatch = selectedCategory === 'all' || battle.category === selectedCategory;
    const difficultyMatch = selectedDifficulty === 'all' || battle.difficulty.toLowerCase() === selectedDifficulty;
    return categoryMatch && difficultyMatch;
  });

  const activeBattles = filteredBattles.filter(b => b.status === 'waiting' || b.status === 'active');
  const completedBattles = filteredBattles.filter(b => b.status === 'completed');

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      // Update battle timers and participant counts
      console.log('Updating battle states...');
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleJoinBattle = (battle) => {
    console.log('Joining battle:', battle.id);
    navigate('/battle-room');
  };

  const handleSpectateBattle = (battle) => {
    console.log('Spectating battle:', battle.id);
    navigate('/battle-room');
  };

  const handleViewBattleDetails = (battle) => {
    setSelectedBattle(battle);
    setShowDetailsModal(true);
  };

  const handleRefresh = () => {
    setIsRefreshing(true);
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1000);
  };

  const handlePullToRefresh = () => {
    if (window.innerWidth <= 768) {
      handleRefresh();
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <BattleQuickJoin />
      
      <main className="pt-16 pb-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Page Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Battle Lobby</h1>
              <p className="text-text-secondary mt-1">
                Join live coding battles and compete with developers worldwide
              </p>
            </div>
            
            {/* Mobile Side Panel Toggle */}
            <div className="lg:hidden">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowSidePanel(!showSidePanel)}
              >
                <Icon name="Menu" size={20} />
              </Button>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row gap-6">
            {/* Main Content */}
            <div className="flex-1">
              {/* Filters */}
              <FilterBar
                selectedCategory={selectedCategory}
                selectedDifficulty={selectedDifficulty}
                onCategoryChange={setSelectedCategory}
                onDifficultyChange={setSelectedDifficulty}
                onRefresh={handleRefresh}
              />

              {/* Pull to Refresh Indicator */}
              {isRefreshing && (
                <div className="flex items-center justify-center py-4 mb-4">
                  <Icon name="RefreshCw" size={20} className="animate-spin text-primary mr-2" />
                  <span className="text-sm text-text-secondary">Refreshing battles...</span>
                </div>
              )}

              {/* Active Battles */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-foreground">
                    Active Battles ({activeBattles.length})
                  </h2>
                  <div className="flex items-center space-x-1 text-success">
                    <div className="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span className="text-sm">Live</span>
                  </div>
                </div>
                
                {activeBattles.length === 0 ? (
                  <div className="text-center py-12 bg-card border border-border rounded-xl">
                    <Icon name="Search" size={48} className="mx-auto mb-4 text-text-secondary" />
                    <h3 className="text-lg font-medium text-foreground mb-2">No Active Battles</h3>
                    <p className="text-text-secondary mb-4">
                      No battles match your current filters. Try adjusting your selection.
                    </p>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setSelectedCategory('all');
                        setSelectedDifficulty('all');
                      }}
                    >
                      Clear Filters
                    </Button>
                  </div>
                ) : (
                  <div 
                    className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                    onTouchStart={handlePullToRefresh}
                  >
                    {activeBattles.map((battle) => (
                      <BattleCard
                        key={battle.id}
                        battle={battle}
                        onJoin={handleJoinBattle}
                        onSpectate={handleSpectateBattle}
                        onViewDetails={handleViewBattleDetails}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Completed Battles */}
              {completedBattles.length > 0 && (
                <div>
                  <h2 className="text-xl font-semibold text-foreground mb-4">
                    Recent Results ({completedBattles.length})
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                    {completedBattles.map((battle) => (
                      <BattleCard
                        key={battle.id}
                        battle={battle}
                        onJoin={handleJoinBattle}
                        onSpectate={handleSpectateBattle}
                        onViewDetails={handleViewBattleDetails}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Side Panel */}
            <div className={`lg:w-80 space-y-6 ${showSidePanel ? 'block' : 'hidden lg:block'}`}>
              <LeaderboardPreview />
              <RecentWinners />
            </div>
          </div>
        </div>
      </main>

      {/* Battle Details Modal */}
      <BattleDetailsModal
        battle={selectedBattle}
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        onJoin={handleJoinBattle}
        onSpectate={handleSpectateBattle}
      />

      {/* Create Battle FAB */}
      <CreateBattleFAB />

      {/* Mobile Side Panel Overlay */}
      {showSidePanel && (
        <div 
          className="fixed inset-0 bg-background/80 backdrop-blur-sm z-[1010] lg:hidden"
          onClick={() => setShowSidePanel(false)}
        />
      )}
    </div>
  );
};

export default BattleLobby;