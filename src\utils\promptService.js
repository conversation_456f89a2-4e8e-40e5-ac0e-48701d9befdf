import { supabase } from './supabaseClient';

const promptService = {
  // Get all prompts with filters
  async getPrompts(filters = {}) {
    try {
      let query = supabase
        .from('prompts')
        .select(`
          *,
          creator:user_profiles!prompts_created_by_fkey(id, username, full_name, avatar_url)
        `)  
        .order('created_at', { ascending: false })

      if (filters.category) {
        query = query.eq('category', filters.category)
      }

      if (filters.difficulty) {
        query = query.eq('difficulty', filters.difficulty)
      }

      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%, description.ilike.%${filters.search}%`)
      }

      const { data, error } = await query

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load prompts' }
    }
  },

  // Get prompt by ID
  async getPromptById(promptId) {
    try {
      const { data, error } = await supabase
        .from('prompts')
        .select(`
          *,
          creator:user_profiles!prompts_created_by_fkey(id, username, full_name, avatar_url)
        `)
        .eq('id', promptId)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load prompt' }
    }
  },

  // Create new prompt (admin/moderator only)
  async createPrompt(promptData) {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('prompts')
        .insert({
          ...promptData,
          created_by: user.user.id
        })
        .select(`
          *,
          creator:user_profiles!prompts_created_by_fkey(id, username, full_name, avatar_url)
        `)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to create prompt' }
    }
  },

  // Update prompt (admin/moderator only)
  async updatePrompt(promptId, updates) {
    try {
      const { data, error } = await supabase
        .from('prompts')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', promptId)
        .select(`
          *,
          creator:user_profiles!prompts_created_by_fkey(id, username, full_name, avatar_url)
        `)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to update prompt' }
    }
  },

  // Delete prompt (admin/moderator only)
  async deletePrompt(promptId) {
    try {
      const { error } = await supabase
        .from('prompts')
        .delete()
        .eq('id', promptId)

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to delete prompt' }
    }
  },

  // Get random prompt for quick battles
  async getRandomPrompt(difficulty = null) {
    try {
      let query = supabase
        .from('prompts')
        .select(`
          *,
          creator:user_profiles!prompts_created_by_fkey(id, username, full_name, avatar_url)
        `)

      if (difficulty) {
        query = query.eq('difficulty', difficulty)
      }

      const { data, error } = await query

      if (error) {
        return { success: false, error: error.message }
      }

      if (!data || data.length === 0) {
        return { success: false, error: 'No prompts available' }
      }

      // Select random prompt
      const randomIndex = Math.floor(Math.random() * data.length)
      return { success: true, data: data[randomIndex] }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to get random prompt' }
    }
  },

  // Get prompt categories
  async getCategories() {
    try {
      const { data, error } = await supabase
        .from('prompts')
        .select('category')
        .order('category')

      if (error) {
        return { success: false, error: error.message }
      }

      // Get unique categories
      const categories = [...new Set(data.map(item => item.category))]
      return { success: true, data: categories }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load categories' }
    }
  }
}

export default promptService