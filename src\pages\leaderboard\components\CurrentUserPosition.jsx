import React from 'react';
import Image from '../../../components/AppImage';
import Icon from '../../../components/AppIcon';

const CurrentUserPosition = ({ user, rank, totalUsers }) => {
  return (
    <div className="bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/30 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Image
              src={user.avatar}
              alt={user.username}
              className="w-16 h-16 rounded-full object-cover border-2 border-primary"
            />
            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-primary rounded-full flex items-center justify-center">
              <span className="text-xs font-bold text-primary-foreground">{rank}</span>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-bold text-foreground">Your Position</h3>
            <p className="text-text-secondary">
              Rank #{rank.toLocaleString()} of {totalUsers.toLocaleString()} users
            </p>
            <div className="flex items-center space-x-4 mt-1 text-sm">
              <span className="flex items-center space-x-1 text-primary">
                <Icon name="Zap" size={14} />
                <span>{user.xp.toLocaleString()} XP</span>
              </span>
              <span className="flex items-center space-x-1 text-success">
                <Icon name="Target" size={14} />
                <span>{user.winRate}% win rate</span>
              </span>
              {user.streak > 0 && (
                <span className="flex items-center space-x-1 text-warning">
                  <Icon name="Flame" size={14} />
                  <span>{user.streak} streak</span>
                </span>
              )}
            </div>
          </div>
        </div>
        
        <div className="text-right">
          <div className="text-2xl font-bold text-primary">#{rank}</div>
          {user.positionChange && (
            <div className={`flex items-center space-x-1 text-sm ${
              user.positionChange > 0 ? 'text-success' : 'text-error'
            }`}>
              <Icon 
                name={user.positionChange > 0 ? 'TrendingUp' : 'TrendingDown'} 
                size={14} 
              />
              <span>{Math.abs(user.positionChange)} this week</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CurrentUserPosition;