import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';
import SolutionCard from './SolutionCard';

const SolutionsTab = ({ solutions, onVote, userVotes }) => {
  const [sortBy, setSortBy] = useState('votes');
  const [filterLanguage, setFilterLanguage] = useState('all');
  const [expandedSolutions, setExpandedSolutions] = useState(new Set());

  const sortOptions = [
    { value: 'votes', label: 'Most Votes' },
    { value: 'time', label: 'Fastest Time' },
    { value: 'recent', label: 'Most Recent' },
    { value: 'alphabetical', label: 'Username A-Z' }
  ];

  const languageOptions = [
    { value: 'all', label: 'All Languages' },
    { value: 'javascript', label: 'JavaScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'cpp', label: 'C++' },
    { value: 'typescript', label: 'TypeScript' }
  ];

  const handleToggleExpand = (solutionId) => {
    const newExpanded = new Set(expandedSolutions);
    if (newExpanded.has(solutionId)) {
      newExpanded.delete(solutionId);
    } else {
      newExpanded.add(solutionId);
    }
    setExpandedSolutions(newExpanded);
  };

  const sortSolutions = (solutions, sortBy) => {
    const sorted = [...solutions];
    switch (sortBy) {
      case 'votes':
        return sorted.sort((a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes));
      case 'time':
        return sorted.sort((a, b) => a.executionTime - b.executionTime);
      case 'recent':
        return sorted.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
      case 'alphabetical':
        return sorted.sort((a, b) => a.user.username.localeCompare(b.user.username));
      default:
        return sorted;
    }
  };

  const filterSolutions = (solutions, language) => {
    if (language === 'all') return solutions;
    return solutions.filter(solution => solution.language.toLowerCase() === language);
  };

  const filteredAndSortedSolutions = sortSolutions(
    filterSolutions(solutions, filterLanguage),
    sortBy
  );

  return (
    <div className="space-y-6">
      {/* Filters and Sort */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center space-x-2 text-sm text-text-secondary">
          <Icon name="Filter" size={16} />
          <span>{filteredAndSortedSolutions.length} solutions</span>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          <Select
            options={languageOptions}
            value={filterLanguage}
            onChange={setFilterLanguage}
            placeholder="Filter by language"
            className="w-full sm:w-48"
          />
          
          <Select
            options={sortOptions}
            value={sortBy}
            onChange={setSortBy}
            placeholder="Sort by"
            className="w-full sm:w-48"
          />
        </div>
      </div>

      {/* Solutions Grid */}
      {filteredAndSortedSolutions.length === 0 ? (
        <div className="text-center py-12">
          <Icon name="Search" size={48} color="var(--color-text-secondary)" className="mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-foreground mb-2">No solutions found</h3>
          <p className="text-text-secondary">Try adjusting your filters to see more results.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredAndSortedSolutions.map((solution) => (
            <SolutionCard
              key={solution.id}
              solution={solution}
              onVote={onVote}
              userVote={userVotes[solution.id]}
              isExpanded={expandedSolutions.has(solution.id)}
              onToggleExpand={handleToggleExpand}
            />
          ))}
        </div>
      )}

      {/* Load More Button */}
      {filteredAndSortedSolutions.length > 0 && (
        <div className="text-center pt-6">
          <Button
            variant="outline"
            iconName="ChevronDown"
            iconPosition="left"
          >
            Load More Solutions
          </Button>
        </div>
      )}
    </div>
  );
};

export default SolutionsTab;