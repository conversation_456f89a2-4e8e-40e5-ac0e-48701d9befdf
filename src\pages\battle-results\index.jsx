import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

import Header from '../../components/ui/Header';
import BattleQuickJoin from '../../components/ui/BattleQuickJoin';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import WinnerAnnouncement from './components/WinnerAnnouncement';
import SolutionsTab from './components/SolutionsTab';
import StatisticsTab from './components/StatisticsTab';
import CommentSystem from './components/CommentSystem';

const BattleResults = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('solutions');
  const [userVotes, setUserVotes] = useState({});
  const [comments, setComments] = useState({});

  // Mock battle data
  const battleInfo = {
    id: 'battle_001',
    title: 'Algorithm Sprint Challenge',
    category: 'Data Structures',
    difficulty: 'Medium',
    duration: '30 minutes',
    completedAt: '2025-01-27T17:30:00Z',
    totalParticipants: 24,
    prompt: `Create a function that finds the longest palindromic substring in a given string.\n\nConstraints:\n- Input string length: 1 ≤ n ≤ 1000\n- Return the longest palindromic substring\n- If multiple palindromes of same length exist, return the first one\n\nExample:\nInput: "babad"\nOutput: "bab" or "aba"`
  };

  const winner = {
    id: 'user_001',
    username: 'CodeNinja42',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    level: 15,
    votes: 18,
    executionTime: 245,
    xpReward: 750,
    rank: 3
  };

  const solutions = [
    {
      id: 'sol_001',
      user: {
        id: 'user_001',
        username: 'CodeNinja42',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        level: 15
      },
      language: 'JavaScript',
      code: `function longestPalindrome(s) {
    if (!s || s.length < 2) return s;
    
    let start = 0;
    let maxLen = 1;
    
    function expandAroundCenter(left, right) {
        while (left >= 0 && right < s.length && s[left] === s[right]) {
            const currentLen = right - left + 1;
            if (currentLen > maxLen) {
                start = left;
                maxLen = currentLen;
            }
            left--;
            right++;
        }
    }
    
    for (let i = 0; i < s.length; i++) {
        expandAroundCenter(i, i);     // odd length palindromes
        expandAroundCenter(i, i + 1); // even length palindromes
    }
    
    return s.substring(start, start + maxLen);
}`,
      executionTime: 245,
      submittedAt: '2 min ago',
      upvotes: 18,
      downvotes: 2,
      totalVotes: 20,
      comments: 5,
      hasPreview: false
    },
    {
      id: 'sol_002',
      user: {
        id: 'user_002',
        username: 'AlgoMaster',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        level: 12
      },
      language: 'Python',
      code: `def longest_palindrome(s):
    if not s:
        return ""
    
    def expand_around_center(left, right):
        while left >= 0 and right < len(s) and s[left] == s[right]:
            left -= 1
            right += 1
        return s[left + 1:right]
    
    longest = ""
    for i in range(len(s)):
        # Check for odd length palindromes
        palindrome1 = expand_around_center(i, i)
        # Check for even length palindromes
        palindrome2 = expand_around_center(i, i + 1)
        
        # Update longest if we found a longer palindrome
        for palindrome in [palindrome1, palindrome2]:
            if len(palindrome) > len(longest):
                longest = palindrome
    
    return longest`,
      executionTime: 312,
      submittedAt: '3 min ago',
      upvotes: 15,
      downvotes: 1,
      totalVotes: 16,
      comments: 3,
      hasPreview: false
    },
    {
      id: 'sol_003',
      user: {
        id: 'user_003',
        username: 'DevQueen',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        level: 18
      },
      language: 'Java',
      code: `public class Solution {
    public String longestPalindrome(String s) {
        if (s == null || s.length() < 2) {
            return s;
        }
        
        int start = 0;
        int maxLen = 1;
        
        for (int i = 0; i < s.length(); i++) {
            int len1 = expandAroundCenter(s, i, i);
            int len2 = expandAroundCenter(s, i, i + 1);
            int len = Math.max(len1, len2);
            
            if (len > maxLen) {
                maxLen = len;
                start = i - (len - 1) / 2;
            }
        }
        
        return s.substring(start, start + maxLen);
    }
    
    private int expandAroundCenter(String s, int left, int right) {
        while (left >= 0 && right < s.length() && s.charAt(left) == s.charAt(right)) {
            left--;
            right++;
        }
        return right - left - 1;
    }
}`,
      executionTime: 189,
      submittedAt: '4 min ago',
      upvotes: 12,
      downvotes: 0,
      totalVotes: 12,
      comments: 7,
      hasPreview: false
    },
    {
      id: 'sol_004',
      user: {
        id: 'user_004',
        username: 'ByteWizard',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        level: 9
      },
      language: 'C++',
      code: `#include <string>
#include <algorithm>
using namespace std;

class Solution {
public:
    string longestPalindrome(string s) {
        if (s.empty()) return "";
        
        int start = 0, maxLen = 1;
        
        for (int i = 0; i < s.length(); i++) {
            int len1 = expandAroundCenter(s, i, i);
            int len2 = expandAroundCenter(s, i, i + 1);
            int len = max(len1, len2);
            
            if (len > maxLen) {
                maxLen = len;
                start = i - (len - 1) / 2;
            }
        }
        
        return s.substr(start, maxLen);
    }
    
private:
    int expandAroundCenter(const string& s, int left, int right) {
        while (left >= 0 && right < s.length() && s[left] == s[right]) {
            left--;
            right++;
        }
        return right - left - 1;
    }
};`,
      executionTime: 156,
      submittedAt: '5 min ago',
      upvotes: 8,
      downvotes: 1,
      totalVotes: 9,
      comments: 2,
      hasPreview: false
    }
  ];

  const battleStats = {
    totalParticipants: 24,
    averageTime: 267,
    completionRate: 87,
    averageRating: 4.2,
    completionTimeline: [
      { time: '0-5min', submissions: 3 },
      { time: '5-10min', submissions: 8 },
      { time: '10-15min', submissions: 12 },
      { time: '15-20min', submissions: 18 },
      { time: '20-25min', submissions: 21 },
      { time: '25-30min', submissions: 24 }
    ],
    languageDistribution: [
      { name: 'JavaScript', value: 8 },
      { name: 'Python', value: 7 },
      { name: 'Java', value: 5 },
      { name: 'C++', value: 3 },
      { name: 'TypeScript', value: 1 }
    ],
    executionTimeDistribution: [
      { range: '0-100ms', count: 2 },
      { range: '100-200ms', count: 5 },
      { range: '200-300ms', count: 8 },
      { range: '300-400ms', count: 6 },
      { range: '400ms+', count: 3 }
    ],
    difficultyFeedback: [
      { rating: 5, count: 8, percentage: 33 },
      { rating: 4, count: 10, percentage: 42 },
      { rating: 3, count: 4, percentage: 17 },
      { rating: 2, count: 2, percentage: 8 },
      { rating: 1, count: 0, percentage: 0 }
    ],
    topPerformers: [
      { username: 'DevQueen', level: 18, score: 950, time: 189 },
      { username: 'CodeNinja42', level: 15, score: 920, time: 245 },
      { username: 'AlgoMaster', level: 12, score: 880, time: 312 },
      { username: 'ByteWizard', level: 9, score: 750, time: 156 },
      { username: 'ScriptKid', level: 11, score: 720, time: 398 }
    ]
  };

  const mockComments = {
    sol_001: [
      {
        id: 'comment_001',
        user: {
          username: 'AlgoExpert',
          avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',
          level: 20
        },
        content: 'Excellent solution! The expand around center approach is very elegant and efficient.',
        timestamp: '2025-01-27T17:32:00Z',
        likes: 5,
        isLiked: false,
        replies: [
          {
            id: 'reply_001',
            user: {
              username: 'CodeNinja42',
              avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
              level: 15
            },
            content: 'Thanks! I spent some time optimizing the time complexity.',
            timestamp: '2025-01-27T17:33:00Z',
            likes: 2,
            isLiked: true
          }
        ]
      }
    ]
  };

  useEffect(() => {
    setComments(mockComments);
  }, []);

  const handleVote = (solutionId, voteType) => {
    setUserVotes(prev => ({
      ...prev,
      [solutionId]: voteType
    }));
  };

  const handleShare = (platform, text) => {
    const url = window.location.href;
    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      copy: () => {
        navigator.clipboard.writeText(`${text} ${url}`);
        alert('Link copied to clipboard!');
      }
    };

    if (platform === 'copy') {
      shareUrls.copy();
    } else {
      window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
  };

  const handleNextBattle = () => {
    navigate('/battle-lobby');
  };

  const handleAddComment = (solutionId, content) => {
    const newComment = {
      id: `comment_${Date.now()}`,
      user: {
        username: 'You',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        level: 10
      },
      content,
      timestamp: new Date().toISOString(),
      likes: 0,
      isLiked: false,
      replies: []
    };

    setComments(prev => ({
      ...prev,
      [solutionId]: [...(prev[solutionId] || []), newComment]
    }));
  };

  const handleReplyComment = (solutionId, commentId, content) => {
    const newReply = {
      id: `reply_${Date.now()}`,
      user: {
        username: 'You',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        level: 10
      },
      content,
      timestamp: new Date().toISOString(),
      likes: 0,
      isLiked: false
    };

    setComments(prev => ({
      ...prev,
      [solutionId]: prev[solutionId].map(comment => 
        comment.id === commentId 
          ? { ...comment, replies: [...(comment.replies || []), newReply] }
          : comment
      )
    }));
  };

  const handleLikeComment = (solutionId, commentId) => {
    setComments(prev => ({
      ...prev,
      [solutionId]: prev[solutionId].map(comment => 
        comment.id === commentId 
          ? { 
              ...comment, 
              isLiked: !comment.isLiked,
              likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1
            }
          : comment
      )
    }));
  };

  const tabs = [
    { id: 'solutions', label: 'Solutions', icon: 'Code', count: solutions.length },
    { id: 'statistics', label: 'Statistics', icon: 'BarChart3' }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <BattleQuickJoin />
      
      <div className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm text-text-secondary mb-6">
            <button onClick={() => navigate('/battle-lobby')} className="hover:text-primary transition-colors">
              Battles
            </button>
            <Icon name="ChevronRight" size={16} />
            <span className="text-foreground">{battleInfo.title}</span>
            <Icon name="ChevronRight" size={16} />
            <span className="text-foreground">Results</span>
          </nav>

          {/* Battle Info Header */}
          <div className="bg-card border border-border rounded-xl p-6 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-foreground mb-2">
                  {battleInfo.title}
                </h1>
                <div className="flex flex-wrap items-center gap-4 text-sm text-text-secondary">
                  <span className="flex items-center space-x-1">
                    <Icon name="Tag" size={16} />
                    <span>{battleInfo.category}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Icon name="Zap" size={16} />
                    <span>{battleInfo.difficulty}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Icon name="Clock" size={16} />
                    <span>{battleInfo.duration}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Icon name="Users" size={16} />
                    <span>{battleInfo.totalParticipants} participants</span>
                  </span>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/battle-room')}
                  iconName="Play"
                  iconPosition="left"
                >
                  View Replay
                </Button>
                <Button
                  variant="default"
                  onClick={handleNextBattle}
                  iconName="Zap"
                  iconPosition="left"
                >
                  Join Next Battle
                </Button>
              </div>
            </div>
          </div>

          {/* Winner Announcement */}
          <WinnerAnnouncement
            winner={winner}
            battleInfo={battleInfo}
            onShare={handleShare}
            onNextBattle={handleNextBattle}
          />

          {/* Tabs */}
          <div className="bg-card border border-border rounded-xl overflow-hidden">
            <div className="border-b border-border">
              <nav className="flex">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 px-6 py-4 font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-foreground hover:bg-muted'
                    }`}
                  >
                    <Icon name={tab.icon} size={18} />
                    <span>{tab.label}</span>
                    {tab.count && (
                      <span className="bg-muted text-text-secondary text-xs px-2 py-1 rounded-full">
                        {tab.count}
                      </span>
                    )}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'solutions' && (
                <SolutionsTab
                  solutions={solutions}
                  onVote={handleVote}
                  userVotes={userVotes}
                />
              )}
              
              {activeTab === 'statistics' && (
                <StatisticsTab battleStats={battleStats} />
              )}
            </div>
          </div>

          {/* Comments Section for Solutions */}
          {activeTab === 'solutions' && (
            <div className="mt-8 space-y-6">
              {solutions.map((solution) => (
                <div key={`comments-${solution.id}`} className="bg-card border border-border rounded-xl p-6">
                  <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                    <Icon name="MessageCircle" size={20} className="mr-2" />
                    Discussion for {solution.user.username}'s Solution
                  </h3>
                  <CommentSystem
                    solutionId={solution.id}
                    comments={comments[solution.id] || []}
                    onAddComment={handleAddComment}
                    onReplyComment={handleReplyComment}
                    onLikeComment={handleLikeComment}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BattleResults;