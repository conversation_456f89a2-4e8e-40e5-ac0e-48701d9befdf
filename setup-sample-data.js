// Sample data setup script for PromptClash
// Run this after setting up Firebase

const samplePrompts = [
  {
    text: "Create a responsive navigation bar with a hamburger menu for mobile devices",
    category: "frontend",
    constraints: {
      maxChars: 500,
      language: "HTML/CSS/JS",
      bannedKeywords: ["bootstrap", "jquery"]
    },
    difficulty: "easy"
  },
  {
    text: "Build a simple calculator that can perform basic arithmetic operations",
    category: "frontend",
    constraints: {
      maxChars: 300,
      language: "JavaScript",
      bannedKeywords: ["eval", "Function"]
    },
    difficulty: "easy"
  },
  {
    text: "Create a todo list with add, delete, and mark as complete functionality",
    category: "frontend",
    constraints: {
      maxChars: 400,
      language: "React",
      bannedKeywords: ["localStorage", "database"]
    },
    difficulty: "medium"
  },
  {
    text: "Design a weather card component that displays temperature, conditions, and location",
    category: "ui/ux",
    constraints: {
      maxChars: 350,
      language: "CSS/HTML",
      bannedKeywords: ["weather API", "fetch"]
    },
    difficulty: "medium"
  },
  {
    text: "Build a simple game of Tic-Tac-Toe with win detection",
    category: "game",
    constraints: {
      maxChars: 600,
      language: "JavaScript",
      bannedKeywords: ["canvas", "WebGL"]
    },
    difficulty: "medium"
  },
  {
    text: "Create a responsive image gallery with lightbox functionality",
    category: "frontend",
    constraints: {
      maxChars: 500,
      language: "HTML/CSS/JS",
      bannedKeywords: ["lightbox library", "modal library"]
    },
    difficulty: "hard"
  },
  {
    text: "Build a real-time chat interface with message bubbles and timestamps",
    category: "ui/ux",
    constraints: {
      maxChars: 700,
      language: "React",
      bannedKeywords: ["socket.io", "firebase"]
    },
    difficulty: "hard"
  },
  {
    text: "Create a drag-and-drop file upload component with progress indicator",
    category: "frontend",
    constraints: {
      maxChars: 600,
      language: "JavaScript",
      bannedKeywords: ["dropzone.js", "upload library"]
    },
    difficulty: "expert"
  }
];

console.log("Sample prompts ready to add to Firestore:");
console.log(JSON.stringify(samplePrompts, null, 2));

// Instructions:
// 1. Go to Firebase Console > Firestore Database
// 2. Click "Start collection" 
// 3. Collection ID: "prompts"
// 4. Add each prompt as a document with auto-generated ID
// 5. Copy the fields from the sample data above 