import { supabase } from './supabaseClient';

const battleService = {
  // Get all battles with filters
  async getBattles(filters = {}) {
    try {
      let query = supabase
        .from('battles')
        .select(`
          *,
          prompt:prompts(*),
          creator:user_profiles!battles_creator_id_fkey(id, username, full_name, avatar_url),
          battle_participants(
            user_id,
            is_spectator,
            user:user_profiles(id, username, full_name, avatar_url, xp)
          )
        `)
        .order('created_at', { ascending: false })

      if (filters.status) {
        query = query.eq('status', filters.status)
      }

      if (filters.difficulty) {
        query = query.eq('prompts.difficulty', filters.difficulty)
      }

      if (filters.category) {
        query = query.eq('prompts.category', filters.category)
      }

      if (filters.isPublic !== undefined) {
        query = query.eq('is_public', filters.isPublic)
      }

      const { data, error } = await query

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load battles' }
    }
  },

  // Get battle by ID
  async getBattleById(battleId) {
    try {
      const { data, error } = await supabase
        .from('battles')
        .select(`
          *,
          prompt:prompts(*),
          creator:user_profiles!battles_creator_id_fkey(id, username, full_name, avatar_url),
          battle_participants(
            user_id,
            is_spectator,
            joined_at,
            user:user_profiles(id, username, full_name, avatar_url, xp)
          ),
          submissions(
            id,
            user_id,
            language,
            status,
            submitted_at,
            execution_time_ms,
            memory_used_mb,
            user:user_profiles(id, username, full_name, avatar_url)
          ),
          chat_messages(
            id,
            content,
            message_type,
            created_at,
            user:user_profiles(id, username, full_name, avatar_url)
          )
        `)
        .eq('id', battleId)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load battle' }
    }
  },

  // Create new battle
  async createBattle(battleData) {
    try {
      const { data, error } = await supabase
        .from('battles')
        .insert({
          ...battleData,
          room_code: await this.generateRoomCode()
        })
        .select(`
          *,
          prompt:prompts(*),
          creator:user_profiles!battles_creator_id_fkey(id, username, full_name, avatar_url)
        `)
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to create battle' }
    }
  },

  // Join battle
  async joinBattle(battleId, isSpectator = false) {
    try {
      const { data, error } = await supabase
        .from('battle_participants')
        .insert({
          battle_id: battleId,
          user_id: (await supabase.auth.getUser()).data.user.id,
          is_spectator: isSpectator
        })
        .select()
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      // Update participant count
      await supabase.rpc('increment_battle_participants', {
        battle_id: battleId
      })

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to join battle' }
    }
  },

  // Leave battle
  async leaveBattle(battleId) {
    try {
      const { error } = await supabase
        .from('battle_participants')
        .delete()
        .eq('battle_id', battleId)
        .eq('user_id', (await supabase.auth.getUser()).data.user.id)

      if (error) {
        return { success: false, error: error.message }
      }

      // Update participant count
      await supabase.rpc('decrement_battle_participants', {
        battle_id: battleId
      })

      return { success: true }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to leave battle' }
    }
  },

  // Update battle status
  async updateBattleStatus(battleId, status, updates = {}) {
    try {
      const { data, error } = await supabase
        .from('battles')
        .update({
          status,
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', battleId)
        .select()
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to update battle status' }
    }
  },

  // Generate unique room code
  async generateRoomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let code
    let isUnique = false

    while (!isUnique) {
      code = ''
      for (let i = 0; i < 6; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length))
      }

      // Check if code already exists
      const { data } = await supabase
        .from('battles')
        .select('id')
        .eq('room_code', code)
        .single()

      if (!data) {
        isUnique = true
      }
    }

    return code
  },

  // Get battle by room code
  async getBattleByRoomCode(roomCode) {
    try {
      const { data, error } = await supabase
        .from('battles')
        .select(`
          *,
          prompt:prompts(*),
          creator:user_profiles!battles_creator_id_fkey(id, username, full_name, avatar_url)
        `)
        .eq('room_code', roomCode.toUpperCase())
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to find battle' }
    }
  },

  // Subscribe to battle changes
  subscribeToBattle(battleId, onUpdate) {
    const channel = supabase
      .channel(`battle-${battleId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'battles',
          filter: `id=eq.${battleId}`
        },
        onUpdate
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'battle_participants',
          filter: `battle_id=eq.${battleId}`
        },
        onUpdate
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'submissions',
          filter: `battle_id=eq.${battleId}`
        },
        onUpdate
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'chat_messages',
          filter: `battle_id=eq.${battleId}`
        },
        onUpdate
      )
      .subscribe()

    return channel
  }
}

export default battleService