import React from 'react';
import Select from '../../../components/ui/Select';

const FilterOptions = ({ categoryFilter, difficultyFilter, onCategoryChange, onDifficultyChange }) => {
  const categoryOptions = [
    { value: 'all', label: 'All Categories' },
    { value: 'algorithms', label: 'Algorithms' },
    { value: 'frontend', label: 'Frontend' },
    { value: 'fullstack', label: 'Full Stack' },
    { value: 'data-structures', label: 'Data Structures' },
    { value: 'system-design', label: 'System Design' }
  ];

  const difficultyOptions = [
    { value: 'all', label: 'All Difficulties' },
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' },
    { value: 'expert', label: 'Expert' }
  ];

  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <Select
        options={categoryOptions}
        value={categoryFilter}
        onChange={onCategoryChange}
        placeholder="Category"
        className="flex-1"
      />
      <Select
        options={difficultyOptions}
        value={difficultyFilter}
        onChange={onDifficultyChange}
        placeholder="Difficulty"
        className="flex-1"
      />
    </div>
  );
};

export default FilterOptions;