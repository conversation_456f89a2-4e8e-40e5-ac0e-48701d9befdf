import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
// Add your imports here
import LoginRegister from "pages/login-register";
import BattleRoom from "pages/battle-room";
import BattleLobby from "pages/battle-lobby";
import UserProfile from "pages/user-profile";
import BattleResults from "pages/battle-results";
import Leaderboard from "pages/leaderboard";
import NotFound from "pages/NotFound";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your routes here */}
        <Route path="/" element={<LoginRegister />} />
        <Route path="/login-register" element={<LoginRegister />} />
        <Route path="/battle-room" element={<BattleRoom />} />
        <Route path="/battle-lobby" element={<BattleLobby />} />
        <Route path="/user-profile" element={<UserProfile />} />
        <Route path="/battle-results" element={<BattleResults />} />
        <Route path="/leaderboard" element={<Leaderboard />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;