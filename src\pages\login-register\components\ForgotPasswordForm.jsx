import React, { useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const ForgotPasswordForm = ({ onBack }) => {
  const { resetPassword, authError, clearError } = useAuth();
  
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleChange = (e) => {
    setEmail(e.target.value);
    // Clear error when user starts typing
    if (authError) clearError();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await resetPassword(email);
      
      if (result.success) {
        setIsSuccess(true);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="text-center space-y-4">
        <div className="w-16 h-16 bg-success/20 rounded-full flex items-center justify-center mx-auto">
          <svg className="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        
        <div>
          <h3 className="text-lg font-semibold text-foreground mb-2">Reset Link Sent!</h3>
          <p className="text-text-secondary mb-4">
            We have sent a password reset link to <strong>{email}</strong>. 
            Please check your email and follow the instructions to reset your password.
          </p>
        </div>

        <div className="space-y-3">
          <Button onClick={onBack} variant="outline" className="w-full">
            Back to Sign In
          </Button>
          
          <p className="text-xs text-text-secondary">
            Did not receive the email? Check your spam folder or{' '}
            <button 
              onClick={() => {
                setIsSuccess(false);
                setEmail('');
              }}
              className="text-primary hover:text-primary/80"
            >
              try again
            </button>
          </p>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Error Display */}
      {authError && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-destructive flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-destructive">{authError}</p>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="text-center mb-6">
        <p className="text-text-secondary">
          Enter your email address and we will send you a link to reset your password.
        </p>
      </div>

      {/* Email Field */}
      <div>
        <label htmlFor="reset-email" className="block text-sm font-medium text-foreground mb-2">
          Email Address
        </label>
        <Input
          id="reset-email"
          name="email"
          type="email"
          value={email}
          onChange={handleChange}
          placeholder="<EMAIL>"
          required
          disabled={isLoading}
        />
      </div>

      {/* Actions */}
      <div className="space-y-3">
        <Button
          type="submit"
          disabled={isLoading || !email}
          className="w-full"
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Sending...</span>
            </div>
          ) : (
            'Send Reset Link'
          )}
        </Button>

        <Button
          type="button"
          variant="outline"
          onClick={onBack}
          disabled={isLoading}
          className="w-full"
        >
          Back to Sign In
        </Button>
      </div>
    </form>
  );
};

export default ForgotPasswordForm;