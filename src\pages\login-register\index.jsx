import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthHeader from './components/AuthHeader';
import AuthBackground from './components/AuthBackground';
import AuthTabs from './components/AuthTabs';
import LoginForm from './components/LoginForm';
import RegisterForm from './components/RegisterForm';
import GitHubAuth from './components/GitHubAuth';
import ForgotPasswordForm from './components/ForgotPasswordForm';

const LoginRegister = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('login');
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  // Check if user is already logged in
  useEffect(() => {
    const user = localStorage.getItem('user');
    if (user) {
      navigate('/battle-lobby');
    }
  }, [navigate]);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setShowForgotPassword(false);
  };

  const handleForgotPassword = () => {
    setShowForgotPassword(true);
  };

  const handleBackToLogin = () => {
    setShowForgotPassword(false);
    setActiveTab('login');
  };

  return (
    <div className="min-h-screen bg-background">
      <AuthHeader />
      <AuthBackground />
      
      <div className="flex items-center justify-center min-h-screen pt-16 px-4">
        <div className="w-full max-w-md">
          {/* Main Auth Container */}
          <div className="bg-card border border-border rounded-xl shadow-elevation p-8">
            {/* Welcome Section */}
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-foreground mb-2">
                {showForgotPassword ? 'Reset Password' : 
                 activeTab === 'login' ? 'Welcome Back' : 'Join PromptClash'}
              </h1>
              <p className="text-text-secondary">
                {showForgotPassword ? 'Enter your email to reset your password' :
                 activeTab === 'login'? 'Sign in to your developer account' : 'Create your competitive coding account'}
              </p>
            </div>

            {/* Auth Forms */}
            {showForgotPassword ? (
              <ForgotPasswordForm onBack={handleBackToLogin} />
            ) : (
              <>
                <AuthTabs activeTab={activeTab} onTabChange={handleTabChange} />
                
                {activeTab === 'login' ? (
                  <LoginForm onForgotPassword={handleForgotPassword} />
                ) : (
                  <RegisterForm />
                )}
                
                <div className="mt-6">
                  <GitHubAuth />
                </div>
              </>
            )}

            {/* Footer Links */}
            {!showForgotPassword && (
              <div className="mt-8 text-center">
                <p className="text-sm text-text-secondary">
                  {activeTab === 'login' ? "Don't have an account? " : "Already have an account? "}
                  <button
                    onClick={() => handleTabChange(activeTab === 'login' ? 'register' : 'login')}
                    className="text-primary hover:text-primary/80 font-medium transition-colors duration-150"
                  >
                    {activeTab === 'login' ? 'Create one here' : 'Sign in here'}
                  </button>
                </p>
              </div>
            )}
          </div>

          {/* Trust Signals */}
          <div className="mt-6 text-center">
            <div className="flex items-center justify-center space-x-4 text-xs text-text-secondary">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-success rounded-full"></div>
                <span>SSL Secured</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>GitHub Verified</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span>Developer Trusted</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginRegister;