import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';

const PromptPanel = ({ prompt, isCollapsed, onToggleCollapse }) => {
  const [activeTab, setActiveTab] = useState('description');

  const tabs = [
    { id: 'description', label: 'Problem', icon: 'FileText' },
    { id: 'examples', label: 'Examples', icon: 'Code' },
    { id: 'constraints', label: 'Constraints', icon: 'AlertCircle' }
  ];

  return (
    <div className={`bg-card border-r border-border transition-all duration-300 ${isCollapsed ? 'w-12' : 'w-full'} h-full flex flex-col`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${prompt.difficulty === 'Easy' ? 'bg-success' : prompt.difficulty === 'Medium' ? 'bg-warning' : 'bg-error'}`}></div>
            <span className="font-semibold text-foreground">{prompt.title}</span>
          </div>
        )}
        <button
          onClick={onToggleCollapse}
          className="p-1 hover:bg-muted rounded transition-colors duration-150"
        >
          <Icon name={isCollapsed ? "ChevronRight" : "ChevronLeft"} size={16} />
        </button>
      </div>

      {!isCollapsed && (
        <>
          {/* Category and Difficulty */}
          <div className="p-4 border-b border-border">
            <div className="flex items-center space-x-4 text-sm">
              <span className="flex items-center space-x-1 text-text-secondary">
                <Icon name="Tag" size={14} />
                <span>{prompt.category}</span>
              </span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                prompt.difficulty === 'Easy' ? 'bg-success/20 text-success' :
                prompt.difficulty === 'Medium'? 'bg-warning/20 text-warning' : 'bg-error/20 text-error'
              }`}>
                {prompt.difficulty}
              </span>
              <span className="flex items-center space-x-1 text-text-secondary">
                <Icon name="Clock" size={14} />
                <span>{prompt.timeLimit} min</span>
              </span>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-border">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-colors duration-150 ${
                  activeTab === tab.id
                    ? 'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-foreground hover:bg-muted'
                }`}
              >
                <Icon name={tab.icon} size={14} />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {activeTab === 'description' && (
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-foreground mb-2">Description</h3>
                  <p className="text-text-secondary leading-relaxed">{prompt.description}</p>
                </div>
                
                <div>
                  <h3 className="font-semibold text-foreground mb-2">Input Format</h3>
                  <p className="text-text-secondary text-sm">{prompt.inputFormat}</p>
                </div>
                
                <div>
                  <h3 className="font-semibold text-foreground mb-2">Output Format</h3>
                  <p className="text-text-secondary text-sm">{prompt.outputFormat}</p>
                </div>
              </div>
            )}

            {activeTab === 'examples' && (
              <div className="space-y-4">
                {prompt.examples.map((example, index) => (
                  <div key={index} className="bg-muted rounded-lg p-3">
                    <h4 className="font-medium text-foreground mb-2">Example {index + 1}</h4>
                    <div className="space-y-2">
                      <div>
                        <span className="text-sm font-medium text-text-secondary">Input:</span>
                        <pre className="bg-background p-2 rounded text-sm font-mono mt-1 overflow-x-auto">
                          {example.input}
                        </pre>
                      </div>
                      <div>
                        <span className="text-sm font-medium text-text-secondary">Output:</span>
                        <pre className="bg-background p-2 rounded text-sm font-mono mt-1 overflow-x-auto">
                          {example.output}
                        </pre>
                      </div>
                      {example.explanation && (
                        <div>
                          <span className="text-sm font-medium text-text-secondary">Explanation:</span>
                          <p className="text-sm text-text-secondary mt-1">{example.explanation}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'constraints' && (
              <div className="space-y-3">
                <h3 className="font-semibold text-foreground">Constraints</h3>
                <ul className="space-y-2">
                  {prompt.constraints.map((constraint, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm text-text-secondary">
                      <Icon name="Dot" size={12} className="mt-1 flex-shrink-0" />
                      <span>{constraint}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default PromptPanel;