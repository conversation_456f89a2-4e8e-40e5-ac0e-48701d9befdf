import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Select from '../../../components/ui/Select';

const BattleHistoryTab = ({ battleHistory }) => {
  const [filters, setFilters] = useState({
    category: 'all',
    result: 'all',
    difficulty: 'all',
    dateRange: '30'
  });
  const [currentPage, setCurrentPage] = useState(1);
  const battlesPerPage = 10;

  const categoryOptions = [
    { value: 'all', label: 'All Categories' },
    { value: 'algorithms', label: 'Algorithms' },
    { value: 'data-structures', label: 'Data Structures' },
    { value: 'web-dev', label: 'Web Development' },
    { value: 'system-design', label: 'System Design' }
  ];

  const resultOptions = [
    { value: 'all', label: 'All Results' },
    { value: 'won', label: 'Won' },
    { value: 'lost', label: 'Lost' },
    { value: 'draw', label: 'Draw' }
  ];

  const difficultyOptions = [
    { value: 'all', label: 'All Difficulties' },
    { value: 'easy', label: 'Easy' },
    { value: 'medium', label: 'Medium' },
    { value: 'hard', label: 'Hard' }
  ];

  const dateRangeOptions = [
    { value: '7', label: 'Last 7 days' },
    { value: '30', label: 'Last 30 days' },
    { value: '90', label: 'Last 3 months' },
    { value: 'all', label: 'All time' }
  ];

  const filteredBattles = battleHistory.filter(battle => {
    if (filters.category !== 'all' && battle.category !== filters.category) return false;
    if (filters.result !== 'all' && battle.result !== filters.result) return false;
    if (filters.difficulty !== 'all' && battle.difficulty !== filters.difficulty) return false;
    
    if (filters.dateRange !== 'all') {
      const daysAgo = parseInt(filters.dateRange);
      const battleDate = new Date(battle.date);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysAgo);
      if (battleDate < cutoffDate) return false;
    }
    
    return true;
  });

  const totalPages = Math.ceil(filteredBattles.length / battlesPerPage);
  const startIndex = (currentPage - 1) * battlesPerPage;
  const paginatedBattles = filteredBattles.slice(startIndex, startIndex + battlesPerPage);

  const getResultIcon = (result) => {
    switch (result) {
      case 'won': return 'Trophy';
      case 'lost': return 'X';
      case 'draw': return 'Minus';
      default: return 'Circle';
    }
  };

  const getResultColor = (result) => {
    switch (result) {
      case 'won': return 'text-success';
      case 'lost': return 'text-error';
      case 'draw': return 'text-warning';
      default: return 'text-text-secondary';
    }
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'easy': return 'text-success';
      case 'medium': return 'text-warning';
      case 'hard': return 'text-error';
      default: return 'text-text-secondary';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-card border border-border rounded-xl p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center space-x-2">
          <Icon name="Filter" size={20} />
          <span>Filters</span>
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Select
            label="Category"
            options={categoryOptions}
            value={filters.category}
            onChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
          />
          <Select
            label="Result"
            options={resultOptions}
            value={filters.result}
            onChange={(value) => setFilters(prev => ({ ...prev, result: value }))}
          />
          <Select
            label="Difficulty"
            options={difficultyOptions}
            value={filters.difficulty}
            onChange={(value) => setFilters(prev => ({ ...prev, difficulty: value }))}
          />
          <Select
            label="Date Range"
            options={dateRangeOptions}
            value={filters.dateRange}
            onChange={(value) => setFilters(prev => ({ ...prev, dateRange: value }))}
          />
        </div>
      </div>

      {/* Battle List */}
      <div className="bg-card border border-border rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-foreground flex items-center space-x-2">
            <Icon name="History" size={20} />
            <span>Battle History</span>
          </h3>
          <span className="text-sm text-text-secondary">
            {filteredBattles.length} battles found
          </span>
        </div>

        {paginatedBattles.length === 0 ? (
          <div className="text-center py-12">
            <Icon name="Search" size={48} color="var(--color-text-secondary)" className="mx-auto mb-4" />
            <p className="text-text-secondary">No battles found matching your filters</p>
          </div>
        ) : (
          <div className="space-y-4">
            {paginatedBattles.map((battle) => (
              <div
                key={battle.id}
                className="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors duration-150"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex items-start space-x-4">
                    <div className={`p-2 rounded-full bg-background ${getResultColor(battle.result)}`}>
                      <Icon name={getResultIcon(battle.result)} size={20} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-semibold text-foreground mb-1">{battle.title}</h4>
                      <div className="flex flex-wrap items-center gap-4 text-sm text-text-secondary">
                        <span className="flex items-center space-x-1">
                          <Icon name="Tag" size={14} />
                          <span className="capitalize">{battle.category.replace('-', ' ')}</span>
                        </span>
                        <span className={`flex items-center space-x-1 ${getDifficultyColor(battle.difficulty)}`}>
                          <Icon name="Zap" size={14} />
                          <span className="capitalize">{battle.difficulty}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Icon name="Users" size={14} />
                          <span>{battle.participants} players</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Icon name="Clock" size={14} />
                          <span>{formatDuration(battle.duration)}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between lg:justify-end gap-4">
                    <div className="text-right">
                      <div className="text-sm font-medium text-foreground">
                        {battle.result === 'won' ? '+' : battle.result === 'lost' ? '-' : ''}{battle.xpChange} XP
                      </div>
                      <div className="text-xs text-text-secondary">
                        {formatDate(battle.date)}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" iconName="Eye">
                        Replay
                      </Button>
                      <Button variant="ghost" size="sm" iconName="Share">
                        Share
                      </Button>
                    </div>
                  </div>
                </div>

                {battle.placement && (
                  <div className="mt-3 pt-3 border-t border-border">
                    <div className="flex items-center space-x-4 text-sm">
                      <span className="text-text-secondary">Final Placement:</span>
                      <span className="font-medium text-foreground">
                        #{battle.placement} of {battle.participants}
                      </span>
                      {battle.votes && (
                        <span className="text-text-secondary">
                          {battle.votes} votes received
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-6 pt-6 border-t border-border">
            <div className="text-sm text-text-secondary">
              Showing {startIndex + 1}-{Math.min(startIndex + battlesPerPage, filteredBattles.length)} of {filteredBattles.length} battles
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                iconName="ChevronLeft"
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                iconName="ChevronRight"
                iconPosition="right"
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BattleHistoryTab;