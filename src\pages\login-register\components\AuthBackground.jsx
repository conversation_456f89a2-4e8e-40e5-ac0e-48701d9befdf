import React from 'react';

const AuthBackground = () => {
  return (
    <div className="fixed inset-0 -z-10 overflow-hidden">
      {/* Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-slate-800"></div>
      
      {/* Coding Pattern Overlay */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 text-6xl font-mono text-primary rotate-12">{'{ }'}</div>
        <div className="absolute top-40 right-20 text-4xl font-mono text-secondary -rotate-12">{'</>'}</div>
        <div className="absolute bottom-40 left-20 text-5xl font-mono text-accent rotate-45">{'[]'}</div>
        <div className="absolute bottom-20 right-10 text-3xl font-mono text-warning -rotate-45">{'()'}</div>
        <div className="absolute top-1/2 left-1/4 text-2xl font-mono text-primary/50">{'function()'}</div>
        <div className="absolute top-1/3 right-1/3 text-2xl font-mono text-secondary/50">{'const x ='}</div>
      </div>
      
      {/* Subtle Grid Pattern */}
      <div 
        className="absolute inset-0 opacity-10"
        style={{
          backgroundImage: `
            linear-gradient(rgba(0, 217, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 217, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      ></div>
      
      {/* Floating Code Blocks */}
      <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 opacity-5">
        <div className="bg-card border border-border rounded-lg p-4 font-mono text-xs text-foreground">
          <div className="text-secondary">if</div>
          <div className="text-primary ml-2">condition</div>
          <div className="text-accent ml-4">return true</div>
        </div>
      </div>
    </div>
  );
};

export default AuthBackground;