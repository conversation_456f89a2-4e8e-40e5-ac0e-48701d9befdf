import React from 'react';

const LoadingState = () => {
  return (
    <div className="space-y-4">
      {[...Array(10)].map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="flex items-center space-x-4 p-4 bg-card border border-border rounded-lg">
            <div className="w-12 h-12 bg-muted rounded-lg"></div>
            <div className="w-12 h-12 bg-muted rounded-full"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted rounded w-1/4"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </div>
            <div className="hidden lg:flex space-x-6">
              <div className="w-16 h-8 bg-muted rounded"></div>
              <div className="w-16 h-8 bg-muted rounded"></div>
              <div className="w-16 h-8 bg-muted rounded"></div>
            </div>
            <div className="flex space-x-2">
              <div className="w-20 h-8 bg-muted rounded"></div>
              <div className="w-24 h-8 bg-muted rounded"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default LoadingState;