import { useRef } from 'react'
import Editor from 'react-monaco-editor'

interface BattleEditorProps {
  value: string
  onChange: (value: string) => void
  language: string
}

const BattleEditor = ({ value, onChange, language }: BattleEditorProps) => {
  const editorRef = useRef<any>(null)

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor
    editor.focus()
  }

  const handleCodeChange = (value: string) => {
    onChange(value)
  }

  const options = {
    selectOnLineNumbers: true,
    roundedSelection: false,
    readOnly: false,
    cursorStyle: 'line' as any,
    automaticLayout: true,
    theme: 'vs-dark',
    fontSize: 14,
    minimap: {
      enabled: false
    },
    scrollBeyondLastLine: false,
    wordWrap: 'on' as any,
    lineNumbers: 'on' as any,
    folding: true,
    showFoldingControls: 'always',
    suggestOnTriggerCharacters: true,
    quickSuggestions: true,
    parameterHints: {
      enabled: true
    }
  } as any

  return (
    <div className="h-full bg-gray-900">
      <div className="bg-gray-800 px-4 py-2 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-300">Language: {language}</span>
            <span className="text-sm text-gray-300">
              Lines: {value.split('\n').length}
            </span>
            <span className="text-sm text-gray-300">
              Characters: {value.length}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-gray-300">Live</span>
          </div>
        </div>
      </div>
      
      <Editor
        height="calc(100vh - 200px)"
        language={language}
        value={value}
        options={options}
        onChange={handleCodeChange}
        editorDidMount={handleEditorDidMount}
      />
    </div>
  )
}

export default BattleEditor 