export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export const validateCode = (
  code: string, 
  constraints: {
    maxChars?: number;
    bannedKeywords?: string[];
    language?: string;
  }
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check character limit
  if (constraints.maxChars && code.length > constraints.maxChars) {
    errors.push(`Code exceeds maximum character limit of ${constraints.maxChars}`);
  }

  // Check banned keywords
  if (constraints.bannedKeywords) {
    const lowerCode = code.toLowerCase();
    for (const keyword of constraints.bannedKeywords) {
      if (lowerCode.includes(keyword.toLowerCase())) {
        errors.push(`Banned keyword detected: ${keyword}`);
      }
    }
  }

  // Check for potentially dangerous code
  const dangerousPatterns = [
    /eval\s*\(/i,
    /innerHTML\s*=/i,
    /document\.write/i,
    /<script/i,
    /javascript:/i,
  ];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(code)) {
      warnings.push('Potentially dangerous code pattern detected');
      break;
    }
  }

  // Check for empty code
  if (!code.trim()) {
    warnings.push('Code is empty');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const extractCodeSections = (code: string) => {
  const sections = {
    html: '',
    css: '',
    js: '',
  };

  const lines = code.split('\n');
  
  for (const line of lines) {
    const trimmed = line.trim();
    if (trimmed.startsWith('// HTML:') || trimmed.startsWith('<!--')) {
      sections.html += line.replace(/^\/\/ HTML:\s*|<!--\s*/, '') + '\n';
    } else if (trimmed.startsWith('// CSS:') || trimmed.startsWith('/*')) {
      sections.css += line.replace(/^\/\/ CSS:\s*|\/\*\s*/, '') + '\n';
    } else if (trimmed.startsWith('// JS:') || trimmed.startsWith('//')) {
      sections.js += line.replace(/^\/\/ JS:\s*|\/\/\s*/, '') + '\n';
    } else {
      // Default to JavaScript if no prefix
      sections.js += line + '\n';
    }
  }

  return {
    html: sections.html.trim(),
    css: sections.css.trim(),
    js: sections.js.trim(),
  };
}; 