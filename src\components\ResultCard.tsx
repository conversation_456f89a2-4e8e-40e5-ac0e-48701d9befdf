import { Battle } from '../types'

interface ResultCardProps {
  battle: Battle
  onBack: () => void
}

const ResultCard = ({ battle, onBack }: ResultCardProps) => {
  const totalVotes = battle.player1_votes + battle.player2_votes
  const player1Percentage = totalVotes > 0 ? (battle.player1_votes / totalVotes) * 100 : 0
  const player2Percentage = totalVotes > 0 ? (battle.player2_votes / totalVotes) * 100 : 0
  
  const winner = battle.player1_votes > battle.player2_votes ? 'Player 1' : 'Player 2'
  const winnerVotes = battle.player1_votes > battle.player2_votes ? battle.player1_votes : battle.player2_votes

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-lg p-8 max-w-2xl w-full border border-gray-700">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🏆</div>
          <h1 className="text-3xl font-bold text-white mb-2">Battle Results</h1>
          <p className="text-gray-400">The votes are in!</p>
        </div>

        {/* Winner Announcement */}
        <div className="bg-gradient-to-r from-yellow-600 to-yellow-800 rounded-lg p-6 mb-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-2">🎉 Winner: {winner}</h2>
          <p className="text-yellow-100">{winnerVotes} votes ({winner === 'Player 1' ? player1Percentage.toFixed(1) : player2Percentage.toFixed(1)}%)</p>
        </div>

        {/* Vote Breakdown */}
        <div className="space-y-4 mb-8">
          <h3 className="text-xl font-semibold text-white mb-4">Vote Breakdown</h3>
          
          {/* Player 1 */}
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-white">Player 1</span>
              <span className="text-lg font-bold text-blue-400">{battle.player1_votes} votes</span>
            </div>
            <div className="w-full bg-gray-600 rounded-full h-3">
              <div 
                className="bg-blue-500 h-3 rounded-full transition-all duration-500"
                style={{ width: `${player1Percentage}%` }}
              />
            </div>
            <div className="text-right mt-1">
              <span className="text-sm text-gray-400">{player1Percentage.toFixed(1)}%</span>
            </div>
          </div>

          {/* Player 2 */}
          <div className="bg-gray-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-white">Player 2</span>
              <span className="text-lg font-bold text-green-400">{battle.player2_votes} votes</span>
            </div>
            <div className="w-full bg-gray-600 rounded-full h-3">
              <div 
                className="bg-green-500 h-3 rounded-full transition-all duration-500"
                style={{ width: `${player2Percentage}%` }}
              />
            </div>
            <div className="text-right mt-1">
              <span className="text-sm text-gray-400">{player2Percentage.toFixed(1)}%</span>
            </div>
          </div>
        </div>

        {/* Battle Info */}
        <div className="bg-gray-700 rounded-lg p-4 mb-8">
          <h3 className="text-lg font-semibold text-white mb-3">Battle Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-400">Prompt:</span>
              <p className="text-white font-medium">{battle.prompt.title}</p>
            </div>
            <div>
              <span className="text-gray-400">Total Votes:</span>
              <p className="text-white font-medium">{totalVotes}</p>
            </div>
            <div>
              <span className="text-gray-400">Duration:</span>
              <p className="text-white font-medium">{battle.duration} minutes</p>
            </div>
            <div>
              <span className="text-gray-400">Status:</span>
              <p className="text-white font-medium capitalize">{battle.status}</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-4">
          <button
            onClick={onBack}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg font-medium transition-colors"
          >
            Back to Battle
          </button>
          <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors">
            New Battle
          </button>
        </div>
      </div>
    </div>
  )
}

export default ResultCard 