import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const FilterBar = ({ 
  selectedCategory, 
  selectedDifficulty, 
  onCategoryChange, 
  onDifficultyChange,
  onRefresh 
}) => {
  const categories = [
    { id: 'all', label: 'All', icon: 'Grid3X3' },
    { id: 'algorithms', label: 'Algorithms', icon: 'Binary' },
    { id: 'frontend', label: 'Frontend', icon: 'Monitor' },
    { id: 'fullstack', label: 'Full Stack', icon: 'Layers' },
    { id: 'database', label: 'Database', icon: 'Database' }
  ];

  const difficulties = [
    { id: 'all', label: 'All Levels', color: 'text-text-secondary' },
    { id: 'easy', label: 'Easy', color: 'text-success' },
    { id: 'medium', label: 'Medium', color: 'text-warning' },
    { id: 'hard', label: 'Hard', color: 'text-error' }
  ];

  return (
    <div className="bg-card border border-border rounded-xl p-4 mb-6">
      {/* Categories */}
      <div className="mb-4">
        <h3 className="text-sm font-medium text-foreground mb-3">Categories</h3>
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => onCategoryChange(category.id)}
              className="flex items-center space-x-2"
            >
              <Icon name={category.icon} size={16} />
              <span>{category.label}</span>
            </Button>
          ))}
        </div>
      </div>

      {/* Difficulties */}
      <div className="mb-4">
        <h3 className="text-sm font-medium text-foreground mb-3">Difficulty</h3>
        <div className="flex flex-wrap gap-2">
          {difficulties.map((difficulty) => (
            <Button
              key={difficulty.id}
              variant={selectedDifficulty === difficulty.id ? 'default' : 'outline'}
              size="sm"
              onClick={() => onDifficultyChange(difficulty.id)}
              className={`${selectedDifficulty === difficulty.id ? '' : difficulty.color}`}
            >
              {difficulty.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="text-xs text-text-secondary">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          iconName="RefreshCw"
          iconPosition="left"
        >
          Refresh
        </Button>
      </div>
    </div>
  );
};

export default FilterBar;