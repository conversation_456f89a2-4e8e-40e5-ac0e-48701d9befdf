import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const WinnerAnnouncement = ({ winner, battleInfo, onShare, onNextBattle }) => {
  const [showCelebration, setShowCelebration] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowCelebration(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const shareText = `🏆 ${winner.username} won the ${battleInfo.title} coding battle! Check out the amazing solution on PromptClash.`;

  const handleShare = (platform) => {
    onShare(platform, shareText);
  };

  return (
    <div className="relative bg-gradient-to-br from-primary/10 to-secondary/10 border border-primary/20 rounded-xl p-6 mb-8 overflow-hidden">
      {/* Celebration Animation */}
      {showCelebration && (
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-primary rounded-full"
              initial={{ 
                x: Math.random() * 100 + '%', 
                y: '100%',
                opacity: 1,
                scale: 0
              }}
              animate={{ 
                y: '-20%',
                opacity: 0,
                scale: 1
              }}
              transition={{ 
                duration: 2,
                delay: Math.random() * 0.5,
                ease: "easeOut"
              }}
            />
          ))}
        </div>
      )}

      <div className="relative z-10">
        {/* Winner Badge */}
        <motion.div 
          className="flex items-center justify-center mb-4"
          initial={{ scale: 0, rotate: -180 }}
          animate={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.8, type: "spring" }}
        >
          <div className="bg-gradient-to-br from-warning to-primary p-4 rounded-full">
            <Icon name="Crown" size={32} color="white" />
          </div>
        </motion.div>

        {/* Winner Info */}
        <div className="text-center mb-6">
          <motion.h2 
            className="text-2xl md:text-3xl font-bold text-foreground mb-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            🎉 Battle Winner! 🎉
          </motion.h2>
          
          <div className="flex items-center justify-center space-x-4 mb-4">
            <div className="w-16 h-16 rounded-full overflow-hidden border-2 border-primary">
              <Image 
                src={winner.avatar} 
                alt={winner.username}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="text-left">
              <h3 className="text-xl font-bold text-primary">{winner.username}</h3>
              <p className="text-text-secondary">Level {winner.level}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-success">{winner.votes}</div>
              <div className="text-sm text-text-secondary">Votes</div>
            </div>
            <div className="bg-card rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-primary">{winner.executionTime}ms</div>
              <div className="text-sm text-text-secondary">Time</div>
            </div>
            <div className="bg-card rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-warning">+{winner.xpReward}</div>
              <div className="text-sm text-text-secondary">XP Earned</div>
            </div>
            <div className="bg-card rounded-lg p-3 border border-border">
              <div className="text-2xl font-bold text-accent">#{winner.rank}</div>
              <div className="text-sm text-text-secondary">Global Rank</div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            variant="default"
            onClick={onNextBattle}
            iconName="Zap"
            iconPosition="left"
            className="flex-1 sm:flex-none"
          >
            Join Next Battle
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleShare('twitter')}
              title="Share on Twitter"
            >
              <Icon name="Twitter" size={18} />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleShare('linkedin')}
              title="Share on LinkedIn"
            >
              <Icon name="Linkedin" size={18} />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleShare('copy')}
              title="Copy Link"
            >
              <Icon name="Copy" size={18} />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WinnerAnnouncement;