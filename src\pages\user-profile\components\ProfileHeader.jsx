import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const ProfileHeader = ({ user, onEditProfile, isEditing }) => {
  const [isAvatarHovered, setIsAvatarHovered] = useState(false);

  const getXPProgress = () => {
    const currentLevelXP = user.level * 1000;
    const nextLevelXP = (user.level + 1) * 1000;
    const progress = ((user.xp - currentLevelXP) / (nextLevelXP - currentLevelXP)) * 100;
    return Math.min(Math.max(progress, 0), 100);
  };

  const formatJoinDate = (date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <div className="bg-card border border-border rounded-xl p-6 mb-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        {/* Avatar and Basic Info */}
        <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
          <div 
            className="relative"
            onMouseEnter={() => setIsAvatarHovered(true)}
            onMouseLeave={() => setIsAvatarHovered(false)}
          >
            <div className="w-24 h-24 lg:w-32 lg:h-32 rounded-full overflow-hidden border-4 border-primary/20">
              <Image
                src={user.avatar}
                alt={`${user.name}'s avatar`}
                className="w-full h-full object-cover"
              />
            </div>
            {isEditing && (
              <div className={`absolute inset-0 bg-black/50 rounded-full flex items-center justify-center transition-opacity duration-200 ${isAvatarHovered ? 'opacity-100' : 'opacity-0'}`}>
                <Icon name="Camera" size={24} color="white" />
              </div>
            )}
            <div className="absolute -bottom-2 -right-2 bg-primary text-primary-foreground rounded-full px-2 py-1 text-xs font-bold">
              {user.level}
            </div>
          </div>

          <div className="text-center sm:text-left">
            <div className="flex items-center gap-2 mb-2">
              <h1 className="text-2xl lg:text-3xl font-bold text-foreground">{user.name}</h1>
              {user.isVerified && (
                <Icon name="BadgeCheck" size={24} color="var(--color-primary)" />
              )}
            </div>
            <p className="text-text-secondary mb-2">@{user.username}</p>
            <p className="text-sm text-text-secondary">
              Joined {formatJoinDate(user.joinDate)}
            </p>
            
            {/* XP Progress */}
            <div className="mt-4 w-full sm:w-64">
              <div className="flex items-center justify-between text-sm mb-1">
                <span className="text-text-secondary">Level {user.level}</span>
                <span className="text-primary font-medium">{user.xp} XP</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-primary to-secondary h-2 rounded-full transition-all duration-500"
                  style={{ width: `${getXPProgress()}%` }}
                ></div>
              </div>
              <p className="text-xs text-text-secondary mt-1">
                {((user.level + 1) * 1000) - user.xp} XP to next level
              </p>
            </div>
          </div>
        </div>

        {/* Stats and Actions */}
        <div className="flex flex-col items-center lg:items-end gap-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-center">
            <div className="bg-background rounded-lg p-3 border border-border">
              <div className="text-xl font-bold text-primary">{user.totalBattles}</div>
              <div className="text-xs text-text-secondary">Battles</div>
            </div>
            <div className="bg-background rounded-lg p-3 border border-border">
              <div className="text-xl font-bold text-success">{user.winRate}%</div>
              <div className="text-xs text-text-secondary">Win Rate</div>
            </div>
            <div className="bg-background rounded-lg p-3 border border-border">
              <div className="text-xl font-bold text-warning">{user.currentStreak}</div>
              <div className="text-xs text-text-secondary">Streak</div>
            </div>
            <div className="bg-background rounded-lg p-3 border border-border">
              <div className="text-xl font-bold text-secondary">#{user.rank}</div>
              <div className="text-xs text-text-secondary">Rank</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant={isEditing ? "outline" : "default"}
              onClick={onEditProfile}
              iconName={isEditing ? "X" : "Edit"}
              iconPosition="left"
            >
              {isEditing ? "Cancel" : "Edit Profile"}
            </Button>
            <Button variant="outline" iconName="Share" iconPosition="left">
              Share
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;