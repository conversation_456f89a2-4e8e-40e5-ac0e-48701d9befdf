import { Prompt } from '../types'

interface PromptCardProps {
  prompt: Prompt
}

const PromptCard = ({ prompt }: PromptCardProps) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'hard':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
      <div className="flex items-start justify-between mb-4">
        <h2 className="text-2xl font-bold text-white">{prompt.title}</h2>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(prompt.difficulty)}`}>
          {prompt.difficulty.toUpperCase()}
        </span>
      </div>
      
      <div className="mb-4">
        <span className="inline-block bg-blue-100 text-blue-800 text-sm px-2 py-1 rounded mr-2">
          {prompt.category}
        </span>
        <span className="text-gray-400 text-sm">
          Created by {prompt.author_id}
        </span>
      </div>
      
      <div className="bg-gray-700 rounded-lg p-4">
        <p className="text-gray-200 leading-relaxed whitespace-pre-wrap">
          {prompt.text}
        </p>
      </div>
      
      <div className="mt-4 flex items-center justify-between text-sm text-gray-400">
        <div className="flex items-center space-x-4">
          <span>👍 {prompt.votes} votes</span>
          <span>📅 {new Date(prompt.created_at).toLocaleDateString()}</span>
        </div>
      </div>
    </div>
  )
}

export default PromptCard 