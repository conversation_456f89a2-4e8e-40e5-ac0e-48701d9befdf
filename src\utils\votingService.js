import { supabase } from './supabaseClient';

const votingService = {
  // Cast vote for a participant
  async castVote(battleId, votedForId, voteType = 'winner') {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('votes')
        .upsert({
          battle_id: battleId,
          voter_id: user.user.id,
          voted_for_id: votedForId,
          vote_type: voteType
        })
        .select()
        .single()

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to cast vote' }
    }
  },

  // Get vote results for a battle
  async getVoteResults(battleId) {
    try {
      const { data, error } = await supabase
        .from('votes')
        .select(`
          *,
          voter:user_profiles!votes_voter_id_fkey(id, username, full_name, avatar_url),
          voted_for:user_profiles!votes_voted_for_id_fkey(id, username, full_name, avatar_url)
        `)
        .eq('battle_id', battleId)

      if (error) {
        return { success: false, error: error.message }
      }

      // Group votes by type and voted_for_id
      const results = data.reduce((acc, vote) => {
        const key = `${vote.vote_type}_${vote.voted_for_id}`
        if (!acc[key]) {
          acc[key] = {
            vote_type: vote.vote_type,
            voted_for: vote.voted_for,
            count: 0,
            voters: []
          }
        }
        acc[key].count++
        acc[key].voters.push(vote.voter)
        return acc
      }, {})

      return { success: true, data: Object.values(results) }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load vote results' }
    }
  },

  // Get user's votes for a battle
  async getUserVotes(battleId) {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, error: 'User not authenticated' }
      }

      const { data, error } = await supabase
        .from('votes')
        .select(`
          *,
          voted_for:user_profiles!votes_voted_for_id_fkey(id, username, full_name, avatar_url)
        `)
        .eq('battle_id', battleId)
        .eq('voter_id', user.user.id)

      if (error) {
        return { success: false, error: error.message }
      }

      return { success: true, data }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to load user votes' }
    }
  },

  // Check if user can vote (is participant but not competitor)
  async canUserVote(battleId) {
    try {
      const { data: user } = await supabase.auth.getUser()
      
      if (!user?.user?.id) {
        return { success: false, canVote: false, reason: 'Not authenticated' }
      }

      // Check if user is a participant
      const { data: participation, error: participationError } = await supabase
        .from('battle_participants')
        .select('is_spectator')
        .eq('battle_id', battleId)
        .eq('user_id', user.user.id)
        .single()

      if (participationError || !participation) {
        return { success: true, canVote: false, reason: 'Not a participant' }
      }

      // Check if user has submitted code (competitors cannot vote)
      const { data: submission, error: submissionError } = await supabase
        .from('submissions')
        .select('id')
        .eq('battle_id', battleId)
        .eq('user_id', user.user.id)
        .eq('status', 'submitted')
        .single()

      if (submissionError && submissionError.code !== 'PGRST116') {
        return { success: false, error: submissionError.message }
      }

      const hasSubmitted = !!submission
      const canVote = participation.is_spectator || !hasSubmitted

      return { 
        success: true, 
        canVote, 
        reason: canVote ? 'Can vote' : 'Competitors cannot vote for themselves'
      }
    } catch (error) {
      if (error?.message?.includes('Failed to fetch') || 
          error?.message?.includes('NetworkError') ||
          error?.name === 'TypeError' && error?.message?.includes('fetch')) {
        return { 
          success: false, 
          error: 'Cannot connect to database. Your Supabase project may be paused or deleted. Please visit your Supabase dashboard to check project status.' 
        }
      }
      return { success: false, error: 'Failed to check voting eligibility' }
    }
  },

  // Subscribe to vote changes
  subscribeToVotes(battleId, onVoteUpdate) {
    const channel = supabase
      .channel(`votes-${battleId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'votes',
          filter: `battle_id=eq.${battleId}`
        },
        onVoteUpdate
      )
      .subscribe()

    return channel
  }
}

export default votingService