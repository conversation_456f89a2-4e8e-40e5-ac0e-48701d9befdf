import { useState, useEffect } from 'react'

interface TimerProps {
  duration: number
  onTimeUp: () => void
}

const Timer = ({ duration, onTimeUp }: TimerProps) => {
  const [timeLeft, setTimeLeft] = useState(duration)

  useEffect(() => {
    if (timeLeft <= 0) {
      onTimeUp()
      return
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [timeLeft, onTimeUp])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getColorClass = () => {
    if (timeLeft <= 30) return 'text-red-500'
    if (timeLeft <= 60) return 'text-yellow-500'
    return 'text-green-500'
  }

  return (
    <div className="flex items-center space-x-2">
      <div className="text-lg font-mono font-bold">⏰</div>
      <div className={`text-lg font-mono font-bold ${getColorClass()}`}>
        {formatTime(timeLeft)}
      </div>
    </div>
  )
}

export default Timer 