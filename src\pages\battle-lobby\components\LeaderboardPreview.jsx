import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const LeaderboardPreview = () => {
  const topPlayers = [
    {
      id: 1,
      rank: 1,
      name: "CodeMaster",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      xp: 15420,
      wins: 89,
      winRate: 78
    },
    {
      id: 2,
      rank: 2,
      name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      xp: 14850,
      wins: 76,
      winRate: 82
    },
    {
      id: 3,
      rank: 3,
      name: "DevNinja",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      xp: 13990,
      wins: 71,
      winRate: 75
    },
    {
      id: 4,
      rank: 4,
      name: "ByteWarrior",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      xp: 12750,
      wins: 65,
      winRate: 73
    },
    {
      id: 5,
      rank: 5,
      name: "StackOverflow",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      xp: 11890,
      wins: 58,
      winRate: 69
    }
  ];

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Icon name="Crown" size={16} className="text-warning" />;
      case 2:
        return <Icon name="Medal" size={16} className="text-text-secondary" />;
      case 3:
        return <Icon name="Award" size={16} className="text-accent" />;
      default:
        return <span className="text-sm font-bold text-text-secondary">#{rank}</span>;
    }
  };

  return (
    <div className="bg-card border border-border rounded-xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-foreground">Top Players</h3>
        <Link to="/leaderboard">
          <Button variant="ghost" size="sm">
            <Icon name="ExternalLink" size={16} />
            View All
          </Button>
        </Link>
      </div>

      {/* Players List */}
      <div className="space-y-3">
        {topPlayers.map((player) => (
          <div key={player.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors duration-150">
            {/* Rank */}
            <div className="flex items-center justify-center w-8">
              {getRankIcon(player.rank)}
            </div>

            {/* Avatar */}
            <div className="w-10 h-10 rounded-full overflow-hidden bg-muted flex-shrink-0">
              <img 
                src={player.avatar} 
                alt={player.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = '/assets/images/no_image.png';
                }}
              />
            </div>

            {/* Info */}
            <div className="flex-1 min-w-0">
              <div className="font-medium text-foreground truncate">
                {player.name}
              </div>
              <div className="text-xs text-text-secondary">
                {player.xp.toLocaleString()} XP • {player.wins} wins
              </div>
            </div>

            {/* Win Rate */}
            <div className="text-right">
              <div className="text-sm font-medium text-success">
                {player.winRate}%
              </div>
              <div className="text-xs text-text-secondary">
                win rate
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="mt-4 pt-4 border-t border-border">
        <div className="text-xs text-text-secondary text-center">
          Updated every 5 minutes
        </div>
      </div>
    </div>
  );
};

export default LeaderboardPreview;