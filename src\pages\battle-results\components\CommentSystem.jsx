import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const CommentSystem = ({ solutionId, comments, onAddComment, onReplyComment, onLikeComment }) => {
  const [showComments, setShowComments] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState('');

  const handleSubmitComment = (e) => {
    e.preventDefault();
    if (newComment.trim()) {
      onAddComment(solutionId, newComment);
      setNewComment('');
    }
  };

  const handleSubmitReply = (e, commentId) => {
    e.preventDefault();
    if (replyText.trim()) {
      onReplyComment(solutionId, commentId, replyText);
      setReplyText('');
      setReplyingTo(null);
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const commentTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - commentTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const CommentItem = ({ comment, isReply = false }) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`${isReply ? 'ml-12 mt-3' : 'mb-4'}`}
    >
      <div className="flex space-x-3">
        <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
          <Image 
            src={comment.user.avatar} 
            alt={comment.user.username}
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="bg-muted rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <span className="font-medium text-foreground text-sm">{comment.user.username}</span>
              <span className="text-xs text-text-secondary">Level {comment.user.level}</span>
              <span className="text-xs text-text-secondary">•</span>
              <span className="text-xs text-text-secondary">{formatTimeAgo(comment.timestamp)}</span>
            </div>
            <p className="text-sm text-foreground">{comment.content}</p>
          </div>
          
          <div className="flex items-center space-x-4 mt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onLikeComment(solutionId, comment.id)}
              iconName="Heart"
              iconPosition="left"
              className={`text-xs ${comment.isLiked ? 'text-error' : 'text-text-secondary'}`}
            >
              {comment.likes}
            </Button>
            
            {!isReply && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                className="text-xs text-text-secondary"
              >
                Reply
              </Button>
            )}
          </div>

          {/* Reply Form */}
          {replyingTo === comment.id && (
            <motion.form
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              onSubmit={(e) => handleSubmitReply(e, comment.id)}
              className="mt-3"
            >
              <div className="flex space-x-2">
                <Input
                  type="text"
                  placeholder={`Reply to ${comment.user.username}...`}
                  value={replyText}
                  onChange={(e) => setReplyText(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="submit"
                  size="sm"
                  disabled={!replyText.trim()}
                  iconName="Send"
                >
                </Button>
              </div>
            </motion.form>
          )}

          {/* Replies */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-3">
              {comment.replies.map((reply) => (
                <CommentItem key={reply.id} comment={reply} isReply={true} />
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="border-t border-border pt-4">
      <Button
        variant="ghost"
        onClick={() => setShowComments(!showComments)}
        iconName="MessageCircle"
        iconPosition="left"
        className="mb-4"
      >
        {comments.length} Comments
      </Button>

      <AnimatePresence>
        {showComments && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-4"
          >
            {/* Add Comment Form */}
            <form onSubmit={handleSubmitComment} className="flex space-x-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center flex-shrink-0">
                <span className="text-xs font-bold text-white">You</span>
              </div>
              <div className="flex-1 flex space-x-2">
                <Input
                  type="text"
                  placeholder="Add a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="flex-1"
                />
                <Button
                  type="submit"
                  size="sm"
                  disabled={!newComment.trim()}
                  iconName="Send"
                >
                </Button>
              </div>
            </form>

            {/* Comments List */}
            <div className="space-y-4">
              {comments.map((comment) => (
                <CommentItem key={comment.id} comment={comment} />
              ))}
            </div>

            {comments.length === 0 && (
              <div className="text-center py-8 text-text-secondary">
                <Icon name="MessageCircle" size={32} className="mx-auto mb-2 opacity-50" />
                <p>No comments yet. Be the first to share your thoughts!</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CommentSystem;