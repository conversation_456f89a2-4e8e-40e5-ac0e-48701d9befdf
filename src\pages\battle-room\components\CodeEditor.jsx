import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CodeEditor = ({ 
  language, 
  onLanguageChange, 
  code, 
  onCodeChange, 
  onSubmit, 
  isSubmitting, 
  validationStatus,
  fontSize,
  onFontSizeChange 
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const editorRef = useRef(null);
  const [lineNumbers, setLineNumbers] = useState(true);
  const [wordWrap, setWordWrap] = useState(false);

  const languages = [
    { value: 'javascript', label: 'JavaScript', icon: 'Code' },
    { value: 'python', label: 'Python', icon: 'Code' },
    { value: 'java', label: 'Java', icon: 'Code' },
    { value: 'cpp', label: 'C++', icon: 'Code' },
    { value: 'html', label: 'HTML', icon: 'Globe' },
    { value: 'css', label: 'CSS', icon: 'Palette' }
  ];

  const fontSizes = [12, 14, 16, 18, 20, 22, 24];

  const handleKeyDown = (e) => {
    // Handle tab key for indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      const start = e.target.selectionStart;
      const end = e.target.selectionEnd;
      const newValue = code.substring(0, start) + '  ' + code.substring(end);
      onCodeChange(newValue);
      
      // Set cursor position after the inserted spaces
      setTimeout(() => {
        e.target.selectionStart = e.target.selectionEnd = start + 2;
      }, 0);
    }
    
    // Handle Ctrl+S for save/submit
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      e.preventDefault();
      onSubmit();
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const getValidationIcon = () => {
    switch (validationStatus) {
      case 'valid':
        return <Icon name="CheckCircle" size={16} className="text-success" />;
      case 'invalid':
        return <Icon name="XCircle" size={16} className="text-error" />;
      case 'warning':
        return <Icon name="AlertTriangle" size={16} className="text-warning" />;
      default:
        return <Icon name="Code" size={16} className="text-text-secondary" />;
    }
  };

  return (
    <div className={`bg-card border-x border-border h-full flex flex-col ${isFullscreen ? 'fixed inset-0 z-[1000]' : ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center space-x-3">
          {/* Language Selector */}
          <select
            value={language}
            onChange={(e) => onLanguageChange(e.target.value)}
            className="bg-background border border-border rounded px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary"
          >
            {languages.map((lang) => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>

          {/* Validation Status */}
          <div className="flex items-center space-x-1">
            {getValidationIcon()}
            <span className="text-xs text-text-secondary">
              {validationStatus === 'valid' ? 'Valid' : 
               validationStatus === 'invalid' ? 'Errors' :
               validationStatus === 'warning' ? 'Warnings' : 'Ready'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Settings */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Icon name="Settings" size={16} />
            </Button>

            {showSettings && (
              <div className="absolute right-0 top-10 w-64 bg-popover border border-border rounded-lg shadow-elevation z-10 p-3">
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-foreground">Font Size</label>
                    <select
                      value={fontSize}
                      onChange={(e) => onFontSizeChange(Number(e.target.value))}
                      className="w-full mt-1 bg-background border border-border rounded px-2 py-1 text-sm"
                    >
                      {fontSizes.map((size) => (
                        <option key={size} value={size}>{size}px</option>
                      ))}
                    </select>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-foreground">Line Numbers</label>
                    <button
                      onClick={() => setLineNumbers(!lineNumbers)}
                      className={`w-10 h-5 rounded-full transition-colors duration-200 ${
                        lineNumbers ? 'bg-primary' : 'bg-muted'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                        lineNumbers ? 'translate-x-5' : 'translate-x-0.5'
                      }`}></div>
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-foreground">Word Wrap</label>
                    <button
                      onClick={() => setWordWrap(!wordWrap)}
                      className={`w-10 h-5 rounded-full transition-colors duration-200 ${
                        wordWrap ? 'bg-primary' : 'bg-muted'
                      }`}
                    >
                      <div className={`w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                        wordWrap ? 'translate-x-5' : 'translate-x-0.5'
                      }`}></div>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Fullscreen Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleFullscreen}
          >
            <Icon name={isFullscreen ? "Minimize2" : "Maximize2"} size={16} />
          </Button>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 relative">
        <div className="absolute inset-0 flex">
          {/* Line Numbers */}
          {lineNumbers && (
            <div className="w-12 bg-muted border-r border-border flex flex-col text-xs text-text-secondary font-mono">
              {code.split('\n').map((_, index) => (
                <div key={index} className="px-2 py-0.5 text-right leading-6">
                  {index + 1}
                </div>
              ))}
            </div>
          )}

          {/* Code Area */}
          <textarea
            ref={editorRef}
            value={code}
            onChange={(e) => onCodeChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className={`flex-1 bg-background text-foreground font-mono resize-none outline-none p-4 leading-6 ${
              wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre overflow-x-auto'
            }`}
            style={{ fontSize: `${fontSize}px` }}
            placeholder="// Start coding here..."
            spellCheck={false}
          />
        </div>
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-3 border-t border-border bg-muted/50">
        <div className="flex items-center space-x-4 text-xs text-text-secondary">
          <span>Lines: {code.split('\n').length}</span>
          <span>Characters: {code.length}</span>
          <span>Language: {languages.find(l => l.value === language)?.label}</span>
        </div>

        <div className="flex items-center space-x-2">
          <span className="text-xs text-text-secondary">Ctrl+S to submit</span>
          <Button
            variant="default"
            size="sm"
            onClick={onSubmit}
            loading={isSubmitting}
            disabled={!code.trim()}
          >
            <Icon name="Send" size={16} />
            Submit Code
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CodeEditor;