import { Routes, Route, Link } from 'react-router-dom'
import { AuthProvider } from './context/AuthContext'
import { BattleProvider } from './context/BattleContext'
import ProtectedRoute from './components/ProtectedRoute'
import Home from './pages/Home'
import BattleRoom from './pages/BattleRoom'
import Create<PERSON>rompt from './pages/CreatePrompt'
import Leaderboard from './pages/Leaderboard'

function App() {
  return (
    <AuthProvider>
      <BattleProvider>
        <div className="min-h-screen bg-gray-900 text-white">
          {/* Navigation */}
          <nav className="bg-gray-800 border-b border-gray-700">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="flex items-center justify-between h-16">
                <div className="flex items-center">
                  <Link to="/" className="flex items-center space-x-2">
                    <span className="text-2xl">🚀</span>
                    <span className="text-xl font-bold">PromptClash</span>
                  </Link>
                </div>
                <div className="flex items-center space-x-4">
                  <Link to="/" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                    Home
                  </Link>
                  <Link to="/create" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                    Create Prompt
                  </Link>
                  <Link to="/leaderboard" className="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                    Leaderboard
                  </Link>
                  <Link to="/battle" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Join Battle
                  </Link>
                </div>
              </div>
            </div>
          </nav>

          {/* Main Content */}
          <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/battle" element={<ProtectedRoute><BattleRoom /></ProtectedRoute>} />
              <Route path="/create" element={<ProtectedRoute><CreatePrompt /></ProtectedRoute>} />
              <Route path="/leaderboard" element={<Leaderboard />} />
            </Routes>
          </main>
        </div>
      </BattleProvider>
    </AuthProvider>
  )
}

export default App 