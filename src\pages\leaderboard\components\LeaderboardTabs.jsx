import React from 'react';
import Button from '../../../components/ui/Button';

const LeaderboardTabs = ({ activeTab, onTabChange }) => {
  const tabs = [
    { id: 'global', label: 'Global', icon: 'Globe' },
    { id: 'weekly', label: 'Weekly', icon: 'Calendar' },
    { id: 'monthly', label: 'Monthly', icon: 'CalendarDays' },
    { id: 'friends', label: 'Friends', icon: 'Users' }
  ];

  return (
    <div className="flex space-x-1 bg-muted p-1 rounded-lg">
      {tabs.map((tab) => (
        <Button
          key={tab.id}
          variant={activeTab === tab.id ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onTabChange(tab.id)}
          iconName={tab.icon}
          iconPosition="left"
          iconSize={16}
          className="flex-1 sm:flex-none"
        >
          {tab.label}
        </Button>
      ))}
    </div>
  );
};

export default LeaderboardTabs;