import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const EmptyState = ({ searchQuery, onClearSearch }) => {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
        <Icon name="Users" size={32} className="text-text-secondary" />
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">
        {searchQuery ? 'No users found' : 'No leaderboard data'}
      </h3>
      <p className="text-text-secondary mb-6 max-w-md mx-auto">
        {searchQuery 
          ? `No users match "${searchQuery}". Try adjusting your search or filters.`
          : 'The leaderboard is currently empty. Be the first to compete!'
        }
      </p>
      {searchQuery && (
        <Button variant="outline" onClick={onClearSearch}>
          Clear Search
        </Button>
      )}
    </div>
  );
};

export default EmptyState;