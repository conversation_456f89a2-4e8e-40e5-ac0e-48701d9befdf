export interface User {
  id: string
  username: string
  email: string
  avatar_url?: string
  xp: number
  total_battles: number
  wins: number
  losses: number
  win_streak: number
  created_at: Date
}

export interface Prompt {
  id: string
  title: string
  text: string
  category: string
  difficulty: 'easy' | 'medium' | 'hard'
  author_id: string
  votes: number
  created_at: Date
}

export interface Battle {
  id: string
  title: string
  prompt: Prompt
  player1_id: string
  player2_id?: string
  player1_code: string
  player2_code: string
  player1_votes: number
  player2_votes: number
  status: 'waiting' | 'active' | 'voting' | 'completed'
  time_limit: number
  duration: number
  participants: User[]
  created_at: Date
  ended_at?: Date
}

export interface Vote {
  id: string
  battle_id: string
  voter_id: string
  winner_id: string
  created_at: Date
}

export interface BattleState {
  currentBattle: Battle | null
  isLoading: boolean
  error: string | null
}

export interface AuthState {
  user: User | null
  isLoading: boolean
  error: string | null
}

export interface LivePreviewData {
  code: string
  output: string
  errors: string[]
} 